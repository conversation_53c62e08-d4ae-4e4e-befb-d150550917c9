<div class="wrapper" style="page-break-after: always;">
    <section>
        <div class="section-content" style="text-align: center; line-height: 25px;">
            <div style="display: flex;">
                <div style="margin-bottom: auto; width: 100%;">
                    <div class="wd-100" style="display: inline-block; margin: 8px auto 10px auto;">
                        <div style="width: 140px; float: left;">
                            <% if(project_logo_file && project_logo_file.id && project_logo_file.file_url) { %>
                                <img src="<%= project_logo_file.file_url %>" style="max-width: 100%">
                            <% } else { %>
                                <img src="data:image/png;base64,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" style="max-width: 100%">
                            <% } %>
                        </div>
                    </div>

                    <div class="wd-100" style="font-size: 25px; font-weight: bold; display: inline-block; margin-bottom: 6px;">
                        <span style="float: left;"><%= title %></span>
                    </div>

                    <div class="wd-100" style="display: inline-block; font-size: 16px; font-weight: bold; color: #97a1b5;">
                        <span style="float: left;">Incident Type: <%= ir.incident_type %></span>
                    </div>
                    <div class="wd-100" style="display: inline-block; font-size: 16px; font-weight: bold; color: #97a1b5; margin-bottom: 4px;">
                        <span style="float: left;"><%= momentTz(+ir.incident_date, 'DD MMM YYYY') %></span>
                    </div>

                    <div class="wd-100">
                        <table class="coverInfoTable">
                            <tbody>
                                <tr>
                                    <td>
                                        <div>
                                            <span>Actual Severity</span>
                                            <span><%= ir.actual_severity %></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span>Open Actions</span>
                                            <span><%= actions_status %></span>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span>Days open</span>
                                            <span><%= daysOpen %></span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="table-border-radius">
                            <table class="table bordered-table coverPageTable">
                                <tbody>
                                    <tr>
                                        <td>
                                            <span>Report Prepared</span>
                                        </td>
                                        <td>
                                            <span>
                                                <%= momentTz(+ir.finalised_at, displayFullDateFormat_slash_DD_MM_YYYY_HH_mm_ss) %>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <span>Date & Time of Incident</span>
                                        </td>
                                        <td>
                                            <span>
                                                <%= momentTz(+ir.incident_date, displayFullDateFormat_slash_DD_MM_YYYY_HH_mm_ss) %>
                                            </span>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            <span>Incident Reported By</span>
                                        </td>
                                        <td>
                                            <span><%= reportedBy %></span>
                                        </td>
                                    </tr>

                                    <tr>
                                        <td>
                                            <span>Project</span>
                                        </td>
                                        <td>
                                            <span><%= project.project_number %> <%= project.name %><% if(project.division_ref && project.division_ref.name) { %> (<%= project.division_ref.name %>) <% } %></span>
                                        </td>
                                    </tr>
                                    <% if (ir.incident_type !== 'Road Traffic') { %>
                                        <tr >
                                            <td>
                                                <span>Location</span>
                                            </td>
                                            <td>
                                                <span><%= ir.location %></span>
                                            </td>
                                        </tr>
                                    <% } %>
                                    <tr >
                                        <td>
                                            <div class="wd-100" style="font-weight: bold; margin-bottom: 3px;">Incident Details</div>
                                        </td>
                                        <td>
                                            <div style="font-size: 12px; text-align: justify; line-height: 15px;" class="wd-100">
                                                <%- (ir.incident_details) ? replaceAll(ir.incident_details, '\n', '<br>') : 'N/A'; %>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
