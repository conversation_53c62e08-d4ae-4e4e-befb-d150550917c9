import {PagedResponse} from './company-time-management.page.model';


export class TimesheetInductionsRow {
    id?: number;
    record_id?: number;
    name?: string;
    first_name?: string;
    last_name?: string;
    user_ref?: number;
    employer_name?: string;
    job_role?: string;
    type_of_employment?: string;
    employment_company?: string;
    // travel_time?: any;
    createdAt?: string;
    weekly_timesheet_ref?: number;

    _weeks?: {
        [day_of_yr: string]: TimesheetWeekDay;
    };
    // _has_pending?: boolean;
    _ts_info?: Array<any>;
    _approved_ts?: Array<number>;
    _all_ts_approved?: boolean;
    // _all_has_ts?: boolean;
    // _has_daily_log?: boolean;
}

export class DailyLogForTimesheet {
    user_id: number;
    day_of_yr: string;
    clock_in?: number;
    clock_out?: number;
    duration_in_sec?: number;
    effective_time?: number;
}

export class TimesheetWeekDay {
    _input?: {
        state?: number;
        val?: number;
        changed?: boolean;
    };
    _input_disable?: boolean;
    _tooltip?: string;

    timesheet: TimesheetLog;
    log: DailyLogForTimesheet;
}

export class TimesheetInductionsPage extends PagedResponse {
    records: Array<TimesheetInductionsRow> = [];
    total_approved_count?: number;
    daily_logs: Array<any> = [];
    timesheets: Array<any> = [];
    weekly_timesheets: Array<any> = [];
}

export class TimesheetLog {
    id?: number;
    project_ref?: number;
    day_of_yr: string;
    user_ref: number;
    status?: number;
    actual_seconds?: number;
    day_seconds?: number;
    night_seconds?: number;
    hours_state?: number;  // 1 - day-hours, 2 - night-hours, 3 split-shift, 4 - holiday, 5 - sick
    travel_seconds?: number;
    overtime_seconds?: number;
    training_seconds?: number;
    manager_auth_seconds?: number;
    price_work_amount?: number;
    // modified_by?: any;
    comments?: Array<any>;
    change_logs?: Array<any>;
    weekly_timesheet_ref?: number;
}

export class WeeklyTimesheetLog {
    id?: number;
    week_end_date?: string;
    project_ref?: number;
    user_ref?: number;
    comments?: Array<any>;
    status?: number;
    modified_by?:number;
    employee_number?: string;
    hourly_rate?: number;
    timesheets?: Array<TimesheetLog>;
}

class TsChangeLog{
    name?: string;
    timestamp?: number;
    user_id?: number;
    /**
     * enum: `system` | `admin`
     */
    origin?: string;
    type?: string;

    entity?: string;
    from?: any;
    to?: any;
}

export const TIMESHEET_STATUS = {
    APPROVED: 2,
    PENDING: 1
};
// 1 - day-hours, 2 - night-hours, 3 split-shift, 4 - holiday, 5 - sick
export const TIME_INPUT_STATE = {
    DAY_HOURS: {v: 1, t: `Hours`, tip: `Day Hours`, iconClass: `text-warning`, icon: 'wb_sunny', is_symbol: false},
    NIGHT_HOURS: {v: 2, t: `Hours`, tip: `Night Hours`, icon: 'clear_night', is_symbol: false},
    SPLIT_SHIFT: {v: 3, t: `Hours`, tip: `Split Shift`, iconClass: `text-blue-jeans`, icon: 'routine', is_symbol: false},
    HOLIDAY: {v: 4, t: `Holiday`, tip: `Holiday`, c: `text-warning1`, is_symbol: true},
    SICK: {v: 5, t: `Sick`, tip: `Sick`, c: `text-danger1`, is_symbol: true}
};

export class TimeInput {
    valid: boolean = true;
    label?: string;
    changed?: boolean = false;
    value?: number = 0;
    state?: number = TIME_INPUT_STATE.DAY_HOURS.v;
}

export const TIME_HOUR_STATES_VALUE: Array<number> = Object.values(TIME_INPUT_STATE).filter(s => !s.is_symbol).map(s => s.v);
