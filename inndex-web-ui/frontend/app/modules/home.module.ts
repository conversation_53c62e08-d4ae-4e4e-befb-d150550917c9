import {NgModule, CUSTOM_ELEMENTS_SCHEMA} from '@angular/core';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap'; // NgbModalModule, NgbDatepickerModule, NgbTimepickerModule, NgbAccordionModule, NgbDropdownModule, NgbCollapseModule
import {FileSelectDirective, FileUploadModule} from 'ng2-file-upload';
import {HomeRoutingModule} from './home.routing';
import {ImageCropperModule} from 'ngx-image-cropper';
import {
    ForgotPasswordComponent,
    ResetPasswordComponent,
    ChangeEmailComponent,
    LoginComponent,
    RegisterComponent,
    UserPreferencesComponent,
    ChangePasswordComponent,
    UseSiteAsComponent,
    CallbackResultViewComponent,
    UserAuthCallbackComponent,
    RestAuthCallBackComponent,
    LivetvLoginComponent,
    AuthBlogArticleComponent,
} from "@app/modules/auth";
import {ReactiveFormsModule, FormsModule} from "@angular/forms";
import {CommonModule} from "@angular/common";
import {BrowserModule} from "@angular/platform-browser";
import {
    UserHomeComponent,
    // NavbarComponent as SignUpStagesNavComponent,
    // SignUpStagePersonalComponent,
    // SignUpStageAddressComponent,
    // UserHealthAnswersComponent,
    // EmploymentDetailComponent,
    // CompetenciesComponent,
    // ResultComponent as SignUpStagesResultComponent,
    ProjectSearchResultComponent,
    AddInductionRequestComponent,
    // ProfilePicComponent,
    MedicationDetailComponent,
    SITE_USER_COMPONENTS,
    COMPANY_SITE_USER_COMPONENTS,
    // UserMedicalAssessmentComponent,
} from "@app/modules/user";
import {
    HomeComponent,
    ManageTbSiteAccessComponent,
    ProjectSiteAdminsComponent,
    ProjectDashboardComponent,
    LiveDashboardComponent,
    PowerBiDashboardComponent,
    WeatherBlocksComponent,
    PieChartComponent,
    BarChartComponent,
    LineChartComponent,
    HeatMapComponent,
    HorizontalBarComponent,
    InductionQuizComponent,
    InductionQuestionComponent,
    AddNewProjectComponent,
    ProjectSettingComponent,
    UserProjectsComponent,
    ViewProjectComponent,
    TimeCalendarComponent,
    VehicleCalendarComponent,
    TravelTimeModifierComponent,
    DownloadRecordsComponent,
    TimeInputComponent,
    CurrencyInputComponent,
    AmendTimesheetComponent,
    ViewTimesheetComponent,
    TimesheetsComponent,
    InductionsComponent,
    DuplicatePhotoReviewComponent,
    Take5sComponent,
    ProjectHeaderComponent,
    MemberListButtonsComponent,
    MembersComponent,
    AllEventsComponent,
    DeliveryManagementComponent,
    TimeManagementHomeComponent,
    ResourcePlannerComponent,
    FatigueViolationsComponent,
    RollCallComponent,
    ViewBookingComponent,
    IntegrationsSetting,
} from "@app/modules/site-admin";
import {
    BlockLoaderComponent,
    ProfilePicViewerComponent,
    ButtonGroupComponent,
    PlaceholderPageComponent,
    FileUploaderComponent,
    QrCodeScannerComponent,
    CropImageUploaderComponent,
    StepperInputComponent,
    PhotoCollageComponent,
    DurationPickerComponent,
    ModalSidebarComponent,
} from "@app/shared";
// import {UserDocUploaderComponent} from "@app/modules/user/sign-up-stages/competencies/doc-sector-row/doc-sector.component";

import {ALL_COMMON_COMPONENTS} from "@app/modules/common";
// import {SUPER_ADMIN_COMPONENTS} from "@app/modules/_admin";
import {SuperAdminHomeComponent} from "./admin/super-admin-home.component";
import {IMPERSONATION_COMPONENTS} from "@app/modules/impersonation";
// import {CKEditorModule} from 'ng2-ckeditor';
import { QuillModule } from 'ngx-quill';
import {NgxDatatableModule} from "@swimlane/ngx-datatable";
import {VgCoreModule} from "@videogular/ngx-videogular/core";
import {VgControlsModule} from "@videogular/ngx-videogular/controls";
import {VgOverlayPlayModule} from "@videogular/ngx-videogular/overlay-play";
import {VgBufferingModule} from "@videogular/ngx-videogular/buffering";
// import {TestComponent} from "@app/modules/user/test.component";
// import { SidebarComponent } from './sidebar/sidebar.component';
import { NgSelectModule } from '@ng-select/ng-select';
import {COMPANY_ADMIN_COMPONENTS} from "@app/modules/company";
import { AgmCoreModule } from '@agm/core';
import {AppConstant} from "@env/environment";

import { AgmJsMarkerClustererModule } from '@agm/js-marker-clusterer';
import { AgmMarkerSpiderModule } from 'agm-spiderfier';
// import { NgxMaskModule } from 'ngx-mask'
import { SignaturePadModule } from '@ng-plus/signature-pad';

import { CORE_RESOLVERS } from '@app/core';
import { PdfJsViewerModule } from 'ng2-pdfjs-viewer';
import { DragulaModule } from 'ng2-dragula';
import { NgxSkeletonLoaderModule } from 'ngx-skeleton-loader';
import { OnBoardingModule } from './on-boarding/on-boarding.module';
import { QRCodeModule } from 'angularx-qrcode';
import { TrimInputDirective, UrlFilterPipe, EmailValidatorDirective } from '@app/shared/pipe-and-directive';
import { TreeviewModule } from 'ngx-treeview';
import { IncidentFileLinkWithDescComponent } from './common/incident-file-link-with-desc/incident-file-link-with-desc.component';
import { ToolInviteModalComponent } from './common/tool-invite-modal/tool-invite-modal.component';
import { ViewInductionComponent } from './site-admin/view-project/view-induction/view-induction.component';
import {
    DocumentsComponent,
    DocumentsTreeComponent,
    DocumentsTableComponent,
    FolderDropdownComponent,
    DeletedDocumentsTableComponent,
} from '@app/modules/documents';
import { DuplicateValidatorDirective } from '@app/shared/pipe-and-directive/input-duplicate-validator.directive';

@NgModule({
    declarations: [
        PhotoCollageComponent,
        //FileSelectDirective,
        // FileUploaderComponent,
        // BlockLoaderComponent,
        ProfilePicViewerComponent,
        ButtonGroupComponent,
        PlaceholderPageComponent,
        QrCodeScannerComponent,
        StepperInputComponent,

        // UserDocUploaderComponent,
        ForgotPasswordComponent,
        ResetPasswordComponent,
        LoginComponent,
        RegisterComponent,
        UserPreferencesComponent,
        ChangePasswordComponent,
        ChangeEmailComponent,
        UseSiteAsComponent,
        UserHomeComponent,
        CallbackResultViewComponent,
        UserAuthCallbackComponent,
        RestAuthCallBackComponent,
        LivetvLoginComponent,
        AuthBlogArticleComponent,


        // SignUpStagesNavComponent,
        // SignUpStagePersonalComponent,
        // SignUpStageAddressComponent,
        // UserHealthAnswersComponent,
        // EmploymentDetailComponent,
        // CompetenciesComponent,
        // SignUpStagesResultComponent,
        // ProfilePicComponent,
        MedicationDetailComponent,

        ProjectSearchResultComponent,
        // TestComponent,
        AddInductionRequestComponent,
        ...SITE_USER_COMPONENTS,
        ...COMPANY_SITE_USER_COMPONENTS,
        // UserMedicalAssessmentComponent,

        // site admin components
        HomeComponent,
        ManageTbSiteAccessComponent,
        ProjectSiteAdminsComponent,
        ProjectDashboardComponent,
        LiveDashboardComponent,
        PowerBiDashboardComponent,
        WeatherBlocksComponent,
        PieChartComponent,
        BarChartComponent,
        LineChartComponent,
        HeatMapComponent,
        HorizontalBarComponent,
        InductionQuizComponent,
        InductionQuestionComponent,
        AddNewProjectComponent,
        ProjectSettingComponent,
        ViewProjectComponent,
        TimeCalendarComponent,
        VehicleCalendarComponent,
        TravelTimeModifierComponent,
        DownloadRecordsComponent,
        TimeInputComponent,
        CurrencyInputComponent,
        AmendTimesheetComponent,
        ViewTimesheetComponent,
        TimesheetsComponent,
        InductionsComponent,
        DuplicatePhotoReviewComponent,
        ResourcePlannerComponent,
        UserProjectsComponent,
        TimeManagementHomeComponent,
        FatigueViolationsComponent,
        RollCallComponent,

        Take5sComponent,
        ProjectHeaderComponent,
        MemberListButtonsComponent,
        MembersComponent,
        AllEventsComponent,
        DeliveryManagementComponent,
        ViewBookingComponent,
        IntegrationsSetting,
        DocumentsComponent,
        DocumentsTreeComponent,
        DocumentsTableComponent,
        FolderDropdownComponent,
        DeletedDocumentsTableComponent,

        // Common
        ...ALL_COMMON_COMPONENTS,

        // SidebarComponent,
        // ...SUPER_ADMIN_COMPONENTS,
        SuperAdminHomeComponent,
        ...COMPANY_ADMIN_COMPONENTS,
        // CropImageUploaderComponent,
        //IMPERSONATION Add shadow user
        ...IMPERSONATION_COMPONENTS,
        UrlFilterPipe,
        TrimInputDirective,
        DurationPickerComponent,
        EmailValidatorDirective,
        IncidentFileLinkWithDescComponent,
        ToolInviteModalComponent,
        ViewInductionComponent,
        ModalSidebarComponent,
        DuplicateValidatorDirective
    ],
    schemas: [CUSTOM_ELEMENTS_SCHEMA],
    imports: [
        NgbModule,
        FileUploadModule,
        NgxDatatableModule,
        //SharedModule,
        NgSelectModule,
        FormsModule,
        ReactiveFormsModule,
        CommonModule,
        BrowserModule,

        VgCoreModule,
        VgControlsModule,
        VgOverlayPlayModule,
        VgBufferingModule,

        // CKEditorModule,
        QuillModule.forRoot({
            theme: 'snow',
        }),
        AgmCoreModule.forRoot({
            apiKey: AppConstant.googleMapSdkAPIKey,
            libraries: ["places"]
        }),
        AgmMarkerSpiderModule,
        SignaturePadModule,
        PdfJsViewerModule,
        AgmJsMarkerClustererModule,
        HomeRoutingModule,
        //SignUpStagesModule,
        ImageCropperModule,
        // NgxMaskModule.forRoot({
        //     showMaskTyped : true,
        // }),
        DragulaModule.forRoot(),
        NgxSkeletonLoaderModule,
        OnBoardingModule,
        QRCodeModule,
        TreeviewModule.forRoot()
    ],
    exports: [
        BlockLoaderComponent
    ],
    providers: [ ...CORE_RESOLVERS ],
    entryComponents: []
})
export class HomeModule {
}
