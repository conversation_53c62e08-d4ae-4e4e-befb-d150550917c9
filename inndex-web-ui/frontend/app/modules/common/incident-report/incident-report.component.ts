import {AfterViewInit, Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import { HttpParams } from '@angular/common/http';
import { Subject } from 'rxjs';
import * as dayjs from 'dayjs';
import { NgbModalConfig, NgbModal, NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import {
    IncidentReportService,
    AuthService,
    UserService,
    User,
    Project,
    isInheritedProjectOfCompany,
    Common,
    ResourceService,
    IncidentActionCategory,
    ProjectIncidentReport,
    HttpService,
    filterData,
    IncidentActionButtons,
    IncidentType,
    ToastService,
} from "@app/core";
import { NgbMomentjsAdapter } from "@app/core/ngb-moment-adapter";
import { ShareToolReportToEmailComponent } from '../share-tool-report-to-email/share-tool-report-to-email.component';
import { AppConstant } from "@env/environment";
import { GenericModalComponent } from '../generic-modal/generic-modal.component';
import { GenericModalConfig } from '../generic-modal/generic-modal.config';
import { GenericConfirmationModalComponent } from '@app/shared';
import { innDexConstant } from '@env/constants';
import {NgModel} from '@angular/forms';

@Component({
    templateUrl: './incident-report.component.html',
    // add NgbModalConfig and NgbModal to the component providers
    providers: [NgbModalConfig, NgbModal],
    styles: [
        '.ir-reviewed{background-color: #FFA500;}'
    ],
    styleUrls: ['./incident-report.component.scss']
})
export class IncidentReportComponent implements OnInit, AfterViewInit {

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    common = new Common();
    from_date: NgbDateStruct = null;
    to_date: NgbDateStruct = null;
    blobType: string = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8';
    selectedIncidentType: string = '';
    multiDropdown: number = 0;
    projects: any = [];
    statusMessages: any = [{"status":1,"status_message":"Open"},{"status":2,"status_message":"Reviewed"},{"status":3,"status_message":"Closed"}];
    divisions: any = [];
    selectedFilterProjects = [];
    selectedFilterStatusMessages = [];
    selectedFilterIncidentTypes = [];
    selectedFilterDivisions = [];
    AppConstant = AppConstant;
    employerId: number = 0;
    records: Array<any> = [];
    ir_row: any = {};
    userFile: any;
    authUser$: User;
    openRecords: number = 0;
    project_category: string = `default`;
    qseSignatureImage;
    employer: any = {};
    is_inherited_project: boolean = false;
    loadingIncidents: boolean = false;
    downloadIRLoading: boolean = false;
    iframe_content_loading: boolean = false;
    companyProjects: Array<Project> = [];
    archivedCompanyProjects: Array<Project> = [];
    companyResolverResponse: any;
    img_link: string = `/images/project-placeholder.png`;
    page = this.common.page;
    companyIncidentsEnabled: boolean = false;
    modalCb: any;
    editCommentData: any;
    editIndex: number;
    editCommentField: string;
    reviewImage: any = {};
    newAction: any = {};
    actionCategories: IncidentActionCategory;
    selectedPersonnel: Array<any> = [];
    actionPriorities: Array<any> = [
        { key: "High", value: "High" },
        { key: "Medium", value: "Medium" },
        { key: "Low", value: "Low" }
    ];
    minDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs().add(1, 'day'));
    maxDate: NgbDateStruct = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
    processLoader: boolean = false;
    newReportLoader: boolean = false;
    editReviewLoader: boolean = false;
    incidentAction: any = {};
    closeout_action_detail: string = '';
    closeout_action_images: Array<any> = [{}];
    allowedMime: Array<any> = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
    hasAllActionClosed: boolean = false;
    actionsColumnTxt: string = '';
    accordionActiveIds: string = '';
    contractorId: number;
    causeOptions: any = {};
    jobRoleList = [];
    causeSubOpts = { 'immediate_causes': [], 'underlying_causes': [], 'root_causes': [] };
    causeOptSelection: any = {
        'immediate_causes': [{ head: null, subhead: null }],
        'underlying_causes': [{ head: null, subhead: null }],
        'root_causes': [{ head: null, subhead: null }],
        'relevant_personnel_user_refs': [{ head: null, subhead: null, id: 0 }]
    };
    incidentTypes: Array<any> = [
        { key: "injury", title: "Injury" },
        { key: "health", title: "Health" },
        { key: "road_traffic", title: "Road Traffic" },
        { key: "damage_loss", title: "Damage or Loss" },
        { key: "violence_abuse", title: "Violence or Abuse" },
        { key: "environmental", title: "Environmental" },
        { key: "service_strike", title: "Service Strike" },
        { key: "near_miss", title: "Near Miss" },
        { key: "unsafe_act_occurrence", title: "Unsafe Act/Occurrence" },
    ];

    incidentReport: ProjectIncidentReport = new ProjectIncidentReport();
    activeStep: number = 0;
    meta_steps_title: any = {
        "Injury": ["Incident Details", "Details of Injured Person", "Injury & Treatment", "Witnesses & Third Parties", "Attachments", "Actions", "Review"],
        "Health": ["Incident Details", "Details of Injured Person", "Injury & Treatment", "Witnesses & Third Parties", "Attachments", "Actions", "Review"],
        "Road Traffic": ["Incident Details", "Location & Environment", "Vehicle  Details", "Driver Details", "Third Party", "Witnesses", "Attachments", "Actions", "Review"],
        "Damage or Loss": ["Incident Details", "Witnesses & Third Parties", "Attachments", "Actions", "Review"],
        "Violence or Abuse": ["Incident Details", "Person(s) Affected", "Witnesses & Third Parties", "Attachments", "Actions", "Review"],
        "Environmental": ["Incident Details", "Witnesses & Third Parties", "Attachments", "Actions", "Review"],
        "Service Strike": ["Incident Details", "Witnesses & Third Parties", "Attachments", "Actions", "Review"],
        "Near Miss" : ["Incident Details", "Witnesses & Third Parties", "Attachments", "Actions", "Review"],
        "Unsafe Act/Occurrence" : ["Incident Details", "Witnesses & Third Parties", "Attachments", "Actions", "Review"]
    };
    meta_incident_type_steps: Array<string> = [];
    incidentReportMetaData: any = {};
    incidentTypeMetaData: any = {};
    inductedUsersList: Array<any> = [];
    IncidentType: typeof IncidentType = IncidentType;
    projectResolverResponse: any = {};
    projectInfo: Project = new Project;
    projectId: number;
    isProjectPortal: boolean = false;
    formsValidationStatus: Array<boolean> = [];
    changingFormStep: Subject<number> = new Subject();
    currentStepName: string = null;
    isStepDropdownOpen: boolean = false;
    isMobileDevice: boolean = false;
    closeOutLoader: boolean = false;
    showSignaturePad: boolean = false;
    showReviewForm: boolean = false;
    showEditForm: boolean = false;
    relevantIncidentDisable: boolean = true;
    closeOutValidStatus: string = '';
    filter: {
        incidentTypes: any[];
        projects: any[];
        divisions: any[];
        status: any[];
        search: string;
    } = {
        incidentTypes: [],
        projects: [],
        divisions: [],
        status: [],
        search:"",
    };
    filterData:filterData[] = this.renderFilterData();
    actionButtonMetaData = {
        isTraining: false,
        actionList: [],
        class: 'material-symbols-outlined',
    };
    loadPowerBiComponent: boolean = false;
    biToolName: string = 'incident_reports';
    biToolLabel: string = `Incident Report`;
    biModalTitle: string = 'Incident Report Dashboard';
    dashboardLevel: string = 'project';
    editReportMode: boolean = false;
    isInitIncidentReport = false;
    loadingInlineIncidentReport: boolean = false;

    constructor(
        private router: Router,
        private activatedRoute: ActivatedRoute,
        private incidentReportService: IncidentReportService,
        private modalService: NgbModal,
        private authService: AuthService,
        private userService: UserService,
        private ngbMomentjsAdapter: NgbMomentjsAdapter,
        private resourceService: ResourceService,
        private httpService: HttpService,
        private toastService: ToastService,
    ) {
        this.from_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.to_date = ngbMomentjsAdapter.dayJsToNgbDate(dayjs());
        this.selectedIncidentType = this.activatedRoute.snapshot.queryParams['incident_type'];
        this.isProjectPortal = this.activatedRoute.snapshot.data.is_project_portal || false;
        this.isMobileDevice = this.httpService.isMobileDevice();
        if (this.isProjectPortal) {
            this.projectResolverResponse = this.activatedRoute.parent.snapshot.data.projectResolverResponse;
            this.projectInfo = this.projectResolverResponse.project;
            this.projectId = this.projectInfo.id;
        }
    }

    dayjs(n: number = 0) {
        let tz = this.projectInfo?.custom_field?.timezone;
        return (n) ? dayjs(n).tz(tz) : dayjs();
    };

    ngOnInit() {
        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });
        if (!this.isProjectPortal) {
            this.activatedRoute.params.subscribe(params => {
                if (this.isCompanyProjectRoute()) {
                    this.projectId = params['projectId'];
                }
                this.employerId = params['employerId'];
            });
            this.employer = this.activatedRoute.snapshot.data.companyResolverResponse.company;
            this.companyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.companyProjects;
            this.archivedCompanyProjects = this.activatedRoute.snapshot.data.companyResolverResponse.archivedCompanyProjects;
            this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
            this.img_link = this.employer.logo_file_url;
            this.companyIncidentsEnabled = this.employer.features_status['incident_reports'];
            this.fetchIncidentActionCatConfig(this.employer.id);

            //Load company level incident reports
            if (!this.isCompanyProjectRoute()) {
                this.initializeTable();
            }
        } else {
            this.initializeTable();
        }

        this.resourceService.getInnDexSettingByName('incident_report_cause_type_en_gb').subscribe((data: any) => {
            if (data.success && data.record && data.record.value) {
                this.causeOptions = data.record.value;
                this.causeSubOpts['immediate_causes'] = this.populateSubOptions(this.causeOptions['immediate_causes']);
                this.causeSubOpts['underlying_causes'] = this.populateSubOptions(this.causeOptions['underlying_causes']);
                this.causeSubOpts['root_causes'] = this.populateSubOptions(this.causeOptions['root_causes']);

            } else {
                const message = 'Something went wrong while fetching options for cause types.';
                this.toastService.show(this.toastService.types.ERROR, message);
            }
        });

        this.actionButtonMetaData.actionList = [
            {
                code: IncidentActionButtons.DASHBOARD,
                name: `View Dashboard`,
                iconClass: this.actionButtonMetaData.class,
                iconClassLabel: 'dashboard',
                enabled: true,
            }
        ];
    }

    populateSubOptions(optionsList) {
        return optionsList.reduce((obj, o) => {
            obj[o.title] = o.options;
            return obj;
        }, {});
    }

    replaceAll(str, find, replace) {
        const escapedFind = find.replace(/([.*+?^=!:${}()|\[\]\/\\])/g, '\\$1');
        return (str || '').replace(new RegExp(escapedFind, 'g'), replace);
    }

    // Will get called for Company route only.
    projectRetrieved(project: Project) {
        this.projectInfo = project;
        this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(project.createdAt));
        this.project_category = project.project_category;
        this.is_inherited_project = isInheritedProjectOfCompany(project, this.employer);
        this.initializeTable();
    }

    pageCallback(pageInfo: { count?: number, pageSize?: number, limit?: number, offset?: number }, isPageChange: boolean) {
        if (!this.isInitIncidentReport) {
          this.isInitIncidentReport = true;
          return;
        }
        this.page.pageNumber = pageInfo.offset;
        this.initializeTable(isPageChange);
    }

    private initializeTable(isPageChange?: boolean) {
        let { search, divisions, projects, incidentTypes, status } = this.filter;

        if (isPageChange) {
          this.loadingInlineIncidentReport = true;
        } else {
          this.loadingIncidents = true;
        }
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`)
            .set('is_inherited_project', `${this.is_inherited_project}`);

        if(search){
            params = params.append('q',`${encodeURIComponent(search)}`);
        }

        if(projects.length){
            params = params.append('project', projects.join(','));
        }

        if(incidentTypes.length){
            params = params.append('incident_type', incidentTypes.join(','));
        }

        if(status.length){
            params = params.append('status', status.join(','));
        }

        if (this.isProjectPortal) {
            this.from_date = this.ngbMomentjsAdapter.dayJsToNgbDate(dayjs(this.projectInfo.createdAt));
            this.project_category = this.projectInfo.project_category;
            this.companyIncidentsEnabled = this.projectResolverResponse.contractor.features_status['incident_reports'];
            if (!this.contractorId) {
                this.contractorId = (this.projectResolverResponse.contractor && this.projectResolverResponse.contractor.id) ? this.projectResolverResponse.contractor.id : this.employerId;
                this.fetchIncidentActionCatConfig(this.contractorId);
            }
        }

        let companyId = (this.employerId ? this.employerId : null);
        if (this.isProjectPortal || this.isCompanyProjectRoute()) {
            this.incidentReportService.getProjectIncidentReports(this.projectId, params).subscribe(this.fetchIncidentResponseHandler.bind(this));
        } else {
            this.incidentReportService.getCompanyIncidentList(companyId, params).subscribe(this.fetchIncidentResponseHandler.bind(this));
        }
    }

    fetchIncidentResponseHandler(data: any) {
        this.openRecords = 0, this.loadingIncidents = false;
        this.loadingInlineIncidentReport = false;
        if (data && data.incident_reports) {
            this.page.totalElements = data.totalCount;
            this.records = data.incident_reports;
            if (this.page.pageNumber === 0) {
                this.setJobRoles();
                this.openRecords = data.open_record_count;
                if (!this.projects.length && !this.isProjectPortal && !this.isCompanyProjectRoute()) {
                    this.projects = data.company_incident_projects;
                    this.filterData = this.renderFilterData();
                }
            }

            let recordIndex = this.records.findIndex(record => (this.ir_row && this.ir_row['id'] && this.ir_row['id'] == record.id));
            this.ir_row = (recordIndex != -1) ? this.records[recordIndex] : {};
            for(let i = 0; i < this.records.length; i++) {
                if (this.records[i].incident_actions.length){
                    this.records[i].incident_actions.forEach(action => {
                        action.close_out.images = this.mapImages(action.close_out.images);
                    })
                }
            }
            return data.incident_reports;
        }
        const message = 'Failed to fetch incident reports';
        this.toastService.show(this.toastService.types.ERROR, message, { data: data });
        return [];
    }

    setJobRoles(): void {
        let country_code = this.isProjectPortal ? (this.projectInfo?.custom_field?.country_code || undefined) : (this.employer?.country_code || undefined);
        let params = { country_code: country_code };
        this.userService.getJobRoles(params).subscribe((data: any) => {
            if (data.success) {
                this.jobRoleList = data.jobrolelist;
            }
        });
    }

    openCloseDropdown(index) {
        this.multiDropdown = this.multiDropdown === index ? 0 : index;
    }

    isChecked(value) {
        return this.multiDropdown === 1 ? this.selectedFilterIncidentTypes.includes(value) :
            this.multiDropdown === 2 ? this.selectedFilterProjects.includes(value) :
                this.multiDropdown === 3 ? this.selectedFilterDivisions.includes(value) :
                    this.multiDropdown === 4 ? this.selectedFilterStatusMessages.includes(value) : false;
    }

    getCloseOutDate(date) {
        return (date && !isNaN(date)) ? this.dayjs(+date).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) : '';
    }

    isCompanyRoute() {
        let routeName = this.activatedRoute.snapshot.url[0].path;
        return routeName === 'company-admin';
    }

    isCompanyProjectRoute() {
        let routeName = this.activatedRoute.snapshot.url[1].path;
        return routeName === 'project-incident-reports';
    }

    isCloseoutAllowed() {
        if (this.isProjectPortal && this.project_category === "default") {
            return true;
        } else if (!this.isProjectPortal && this.project_category === "company-project") {
            return true;
        } else {
            return false;
        }
    }

    @ViewChild('shareIncidentReportModal') shareIncidentReportModalRef: ShareToolReportToEmailComponent;
    openShareincidentDetailModal(row) {
        this.shareIncidentReportModalRef.openEmailFormModal(row);
    }

    shareIncidentReport(event) {
        this.incidentReportService.shareIncidentReport(event.req, event.reportId).subscribe((res: any) => {
            event.cb(res);
        });
    }

    headSelected(field, i, $event:any={}) {
        this.causeOptSelection[field][i].subhead = null;
        if (field == 'relevant_personnel_user_refs' && $event.selectedRecord && $event.selectedRecord.job_role) {
            this.causeOptSelection[field][i].head = $event.selectedRecord.name;
            this.causeOptSelection[field][i].subhead = $event.selectedRecord.job_role;
            this.causeOptSelection[field][i].id = $event.selectedRecord.user_ref;
            this.subHeadSelected('relevant_personnel_user_refs', i);
            this.populateSelectedPersonnel();
        }
    }

    subHeadSelected(field, i) {
        if (i + 1 == this.causeOptSelection[field].length) {
            this.causeOptSelection[field].push({ head: null, subhead: null });
        }
    }

    removeCauseRow(field, i: number) {
        this.causeOptSelection[field].splice(i, 1);
        this.populateSelectedPersonnel();
    }

    populateSelectedPersonnel() {
        this.selectedPersonnel = [
            ...(this.ir_row['relevant_personnel_user_refs'] || []).map(b => b.id),
            ...(this.causeOptSelection['relevant_personnel_user_refs'] || []).map(b => b.id)
        ];
    }

    resetCauseOptSelection() {
        this.causeOptSelection = {
            'immediate_causes': [{ head: null, subhead: null }],
            'underlying_causes': [{ head: null, subhead: null }],
            'root_causes': [{ head: null, subhead: null }],
            'relevant_personnel_user_refs': [{ head: null, subhead: null, id: 0 }]
        };
        this.populateSelectedPersonnel();
    }

    @ViewChild('reviewPopupHtml') private reviewPopupHtmlModal: GenericModalComponent
    private reviewPopupHtmlRef: TemplateRef<any>;
    openReviewModal(row, c, defaultOpenTabs = '') {
        this.showReviewForm = true
        this.ir_row = row;
        this.reviewPopupHtmlModalConfig.modalTitle = `Incident #${row?.record_ref} Review`
        this.populateSelectedPersonnel();
        this.resetCauseOptSelection();
        return this.reviewPopupHtmlModal.open();
    }

    @ViewChild('reviewForm') reviewForm;
    public reviewPopupHtmlModalConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: 'Close',
        primaryTrailingBtnAction: () => {
            this.reviewPopupHtmlModal.close();
            if(this.reviewForm) this.reviewForm.reset()
            this.showReviewForm = false;
            return true;
        },
        closeTrailingBtnAction: () =>{
            if(this.reviewForm) this.reviewForm.reset()
            this.showReviewForm = false;
            return true;
        },
        showCancelTrailingBtn: false,
        modalOptions: {
            size: 'lg',
            windowClass: 'reviewForm'
        }
    }

    openHtmlModal(content, size = 'md', cb = () => { }) {
        this.newAction = {};
        const modalRef = this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size
        });
        this.modalCb = cb;
        return modalRef;
    }


    @ViewChild('addActionHtml') private addActionModalRefGenericModal: GenericModalComponent
    private addActionModalHtmlRef: TemplateRef<any>;
    @ViewChild('addActionForm') addActionForm;
    addAction(){
        if(this.addActionForm) {
            this.addActionForm.reset();
            this.addActionForm.form.statusChanges.subscribe(status => {
                this.addActionModalRefConfig.primaryBtnDisabled = status !== 'VALID';
            });
        }
        this.addActionForm.reset();
        this.addActionModalRefGenericModal.open()
    }

    public addActionModalRefConfig: GenericModalConfig = {
        modalTitle: 'Add Action',
        primaryTrailingBtnLabel: 'Assign',
        primaryTrailingBtnAction: () => {
            this.addIncidentAction();
            return true;
        },
        primaryBtnDisabled: true,
        modalOptions: {
            size: 'md',
            windowClass: 'reviewForm'
        }
    }

    @ViewChild('closeOutHtml') private closeOutHtmlModalRefGenericModal: GenericModalComponent
    private closeOutModalHtmlRef: TemplateRef<any>;
    closeOutModal(row) {
        this.accordionActiveIds = ''
        this.ir_row = row;
        this.closeOutModalRefConfig.modalTitle = `Incident #${this.ir_row?.record_ref}`
        return this.closeOutHtmlModalRefGenericModal.open();
    }

    public closeOutModalRefConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: 'Yes',
        primaryTrailingBtnAction: () => {
            this.showSignaturePad = false
            setTimeout(() => {
                this.showSignaturePad = true
                this.clearSignature();
            }, 0);
            this.closeOutFormModal()
            return true;
        },
        cancelTrailingBtnAction: () => {
            this.closeOutHtmlModalRefGenericModal.close()
            return true;
        },
        modalOptions: {
            size: 'md',
        }
    }

    @ViewChild('closeOutFormHtml') private closeOutFormHtmlModalRefGenericModal: GenericModalComponent
    private closeOutFormModalHtmlRef: TemplateRef<any>;
    closeOutFormModal() {
        if (this.closeOutForm) {
            this.closeOutForm.form.controls?.closeout_comment?.setValue('')
            this.closeOutForm?.form.markAsUntouched();
            this.updatePrimaryBtnDisabled()
            this.closeOutForm.form.statusChanges.subscribe(status => {
                this.closeOutValidStatus = status
                this.updatePrimaryBtnDisabled()
            });
        }
        return this.closeOutFormHtmlModalRefGenericModal.open();
    }

    @ViewChild('closeOutForm') closeOutForm

    public closeOutFormModalRefConfig: GenericModalConfig = {
        modalTitle: 'Incident Close Out',
        primaryTrailingBtnLabel: 'Close Out',
        primaryTrailingBtnAction: () => {
            this.closeOutRequest(this.closeOutForm)
            return true;
        },
        closeTrailingBtnAction: () => {
            this.showSignaturePad = false
            return true;
        },
        cancelTrailingBtnAction: () => {
            this.showSignaturePad = false
            this.closeOutFormHtmlModalRefGenericModal.close()
            return true;
        },
        primaryBtnDisabled: true,
        modalOptions: {
            size: 'md',
        }
    }

    updatePrimaryBtnDisabled() {
        this.closeOutFormModalRefConfig.primaryBtnDisabled = this.closeOutValidStatus !== 'VALID' || !this.qseSignatureImage
    }

    closeOutRequest(form) {
        if (!form.valid) {
            const message = 'Modal form is not valid';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }

        this.closeOutLoader = true;
        let request = {
            closeout_request: {
                closeout_comment: form.value.closeout_comment,
                closeout_sign: this.qseSignatureImage
            }
        };
        let projectId = this.projectId || (this.ir_row?.project_ref?.id);
        this.qseSignatureImage = null;
        this.incidentReportService.closeOutIncidentReport(projectId, request, this.ir_row.id).subscribe(this.responseHandler.bind(this));
    }

    responseHandler(out: any) {
        this.closeOutLoader = false;
        if (out.success) {
            this.closeOutHtmlModalRefGenericModal.close();
            const message = 'Incident report successfully closed.';
            this.toastService.show(this.toastService.types.SUCCESS, message);
            this.showSignaturePad = false
        } else {
            const message = out.message || 'Failed to close out incident report.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        }
        this.initializeTable(true);
        this.closeOutHtmlModalRefGenericModal.close()
        this.closeOutFormHtmlModalRefGenericModal.close()
    }

    saveSignature(data) {
        this.qseSignatureImage = data;
        this.updatePrimaryBtnDisabled()
    }

    clearSignature() {
        this.qseSignatureImage = null;
        this.updatePrimaryBtnDisabled()
    }

    saveReviewField(form, $modalLoader, field, ir) {
        let comment = form.value[field];
        let causeOptsReq = [], tempArr = [];
        (this.causeOptSelection[field] || []).map(row => {
            if (row.head && row.subhead && !tempArr.includes(`${row.head}: ${row.subhead}`)) {
                tempArr.push(`${row.head}: ${row.subhead}`);
                let data = {
                    user_ref: this.authUser$.id,
                    user_name: this.authUser$.name,
                    added_at: dayjs().valueOf(),
                    comment: null,
                    head: row.head,
                    subhead: row.subhead
                };
                if (row.id) { data['id'] = row.id }
                causeOptsReq.push(data);
            }
            return row;
        });
        if (!comment && !causeOptsReq.length) {
            const message = 'You must select a user to proceed.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }
        let commentReq = [];
        if (comment) {
            commentReq = [{
                user_ref: this.authUser$.id,
                user_name: this.authUser$.name,
                added_at: dayjs().valueOf(),
                comment: comment
            }];
        }
        let request:any = {};
        request.action = 'saveReviewField';
        request[field] = [
            ...commentReq,
            ...causeOptsReq,
            ...ir[field]
        ];
        this.incidentReportService.updateIncidentReport(request, this.projectId, ir.id).subscribe(out => {
            if (!out.success) {
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            }
            this.ir_row[field] = request[field];
            if (form.controls[field]) { form.controls[field].reset(); }
            this.causeOptSelection[field] = [{ head: null, subhead: null }];
            if (field == 'relevant_personnel_user_refs') {
                this.causeOptSelection[field] = [{ head: null, subhead: null, id: 0 }];
                this.populateSelectedPersonnel();
            }
            this.initializeTable(true);
        });
    }

    editHeadSelected() {
        this.editCommentData.subhead = null;
    }

    editPersonnelSelected($event) {
        if ($event?.selectedRecord && $event.selectedRecord.job_role) {
            this.editCommentData.head = $event.selectedRecord.name;
            this.editCommentData.subhead = $event.selectedRecord.job_role;
            this.editCommentData.id = $event.selectedRecord.user_ref;
        }
    }

    @ViewChild('editCommentFormHtml') private editCommentFormHtmlModal: GenericModalComponent
    editCommentModal(commentObj, idx, field) {
        this.showEditForm = true;
        this.editCommentData = Object.assign({}, commentObj);
        this.editIndex = idx;
        this.editCommentField = field;
        if(this.editCommentForm){
            this.editCommentForm.form.statusChanges.subscribe(status => {
                this.editCommentFormModalRefConfig.primaryBtnDisabled = status !== 'VALID'
            })
        }
        this.editCommentFormHtmlModal.open()
    }

    @ViewChild('editCommentForm') editCommentForm
    public editCommentFormModalRefConfig: GenericModalConfig = {
        modalTitle: 'Edit Comment',
        primaryTrailingBtnLabel: 'Save',
        bodyClass: {
            'overflow': 'unset',
        },
        primaryTrailingBtnAction: () => {
            this.editCommentRequest(this.editCommentForm,this.ir_row)
            return true;
        },
        cancelTrailingBtnAction:()=>{
            this.editCommentFormHtmlModal.close();
            this.showEditForm = false;
            return true;
        },
        primaryBtnDisabled: true,
        modalOptions: {
            size: 'lg',
        }
    }

    editCommentRequest(form, ir) {
        if (!form.valid) {
            console.log('Modal form is not valid');
            return;
        }
        this.editReviewLoader = true;
        let request:any = {};

        request.action = 'editComment';
        this.ir_row[this.editCommentField][this.editIndex] = this.editCommentData;
        request[this.editCommentField] = [
            ...this.ir_row[this.editCommentField]
        ];
        this.incidentReportService.updateIncidentReport(request,this.projectId, ir.id).subscribe(out => {
            if (!out.success) {
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            }
            this.editCommentData = {}; this.editIndex = null; this.editCommentField = null;
            this.initializeTable(true);
            this.populateSelectedPersonnel();
            this.editCommentFormHtmlModal.close();
            this.showEditForm = false;
            this.editReviewLoader = false;
        });
    }


    deleteCommentWarningConfirm(idx, field) {
        this.editIndex = idx;
        this.editCommentField = field;
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Delete',
            title: `Are you sure you want to delete this entry?`,
            confirmLabel: 'Delete',
            onConfirm: () => {
                let request: any = {};
                this.editReviewLoader = true;
                this.ir_row[this.editCommentField].splice(this.editIndex, 1);
                request[this.editCommentField] = [
                    ...this.ir_row[this.editCommentField]
                ];
                if (this.editCommentField == 'review_photo_ids' && request[this.editCommentField].length) {
                    request[this.editCommentField] = request[this.editCommentField].map(m => {
                        m.file = (m.file.id) ? m.file.id : m.file;
                        return m;
                    });
                }

                request.action = 'deleteComment';
                this.incidentReportService.updateIncidentReport(request, this.projectId, this.ir_row.id).subscribe(out => {
                    if (!out.success) {
                        const message = out.message || 'Failed to update data.';
                        this.toastService.show(this.toastService.types.ERROR, message, { data: out });
                    }
                    this.editIndex = null; this.editCommentField = null;
                    this.initializeTable(true);
                    this.populateSelectedPersonnel();
                    this.editReviewLoader = false;
                });
            }
        });
    }

    reviewPhotoUploadDone(data: any) {
        if (data && data.userFile && data.userFile.id) {
            this.reviewImage = data.userFile;
        }
    }

    fileDeleteDone($event, type = 'reviewImage') {
        if ($event && $event.userFile && $event.userFile.id) {
            if (type == 'reviewImage') {
                this.reviewImage = {};
            } else {
                this.closeout_action_images = this.closeout_action_images.filter(r => (r.id !== $event.userFile.id));
            }
        }
    }

    saveAttachmentFiles(form, $modalLoader, ir, imgUploader) {
        let description = form.value.attachment_description;
        if (!description) {
            console.log('Modal form is not valid');
            return;
        }
        let reviewPhotoIds = (ir.review_photo_ids || []).reduce((arr, value) => {
            if (value && value.file && (typeof value.file === "number")) {
                arr.push(value)
            } else if (value && value.file && (typeof value.file === "object") && value.file.id) {
                value.file = value.file.id;
                arr.push(value);
            }
            return arr;
        }, []);

        let request: any = {
            review_photo_ids: [
                {
                    user_ref: this.authUser$.id,
                    user_name: this.authUser$.name,
                    added_at: dayjs().valueOf(),
                    description: description,
                    file: this.reviewImage.id
                },
                ...reviewPhotoIds
            ]
        };
        request.action = 'saveAttachmentFiles';
        this.incidentReportService.updateIncidentReport(request, this.projectId, ir.id).subscribe(out => {
            if (!out.success) {
                const message = out.message || 'Failed to store data.';
                this.toastService.show(this.toastService.types.ERROR, message, { data: out });
            }
            this.ir_row['review_photo_ids'] = out.incident_report.review_photo_ids;
            form.reset(); this.reviewImage = {}; imgUploader.completed = false;
            this.initializeTable(true);
        });
    }

    getEpochTime(date = '') {
        return date !== '' ? dayjs(+date).valueOf() : '';
    }

    @ViewChild('viewIncidentHtml') private viewIncidentHtmlGenericModal: GenericModalComponent
    viewIncidentReport(row) {
        this.ir_row = row
        this.viewIncidentConfig.modalTitle = `Incident & Investigation Report #${row?.record_ref}`;
        this.iframe_content_loading = true;
        this.downloadIncidentReport(row, 'html').subscribe((html: any) => {
            let iframe = document.getElementById('incidentReportFrame') as HTMLIFrameElement;
            let doc = iframe.contentDocument;
            doc.write(html);
            this.iframe_content_loading = false;
        });
        return this.viewIncidentHtmlGenericModal.open()
    }


    public viewIncidentConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: 'OK',
        primaryTrailingBtnAction: () => {
            this.viewIncidentHtmlGenericModal.close();
            return true;
        },
        showCancelTrailingBtn: false,
        modalOptions: {
            size: 'lg',
            windowClass: "xl-modal"
        }
    }

    downloadIncidentReport(row, target = 'pdf') {
        this.downloadIRLoading = (target == 'pdf');
        let body = {
            createdAt: row.createdAt,
            companyId: this.employerId ? this.employerId : null,
            type: target
        };
        return this.incidentReportService.downloadIncidentReport(body, row.id, () => {
            this.downloadIRLoading = false;
        });
    }

    onTagUserRefSelected($event) {
        if($event && $event.selectedRecord)
            this.newAction.tag_user_ref = {
                id: $event?.selectedRecord.user_ref,
                name: $event?.selectedRecord.name,
            };
    }

    addIncidentAction() {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Assign Action',
            title: `Are you sure you want to assign an action to <span class="fw-500">${this.newAction.tag_user_ref.name}</span>?`,
            confirmLabel: 'Assign',
            onConfirm: () => {
                this.newAction.id = this.common.getUniqueId();
                this.newAction.close_out = {};
                this.newAction.assigned_by_user_ref = this.authUser$.id;
                this.newAction.due_date = this.ngbMomentjsAdapter.ngbDateToDayJs(this.newAction.due_date).valueOf();
                let existingActions = (this.ir_row['incident_actions'] || []).reduce((arr, item) => {
                    if (item.close_out && item.close_out.images && item.close_out.images.length) {
                        item.close_out.images = (item.close_out.images || []).map(img => img.id).filter(id => id != null);
                    }
                    arr.push(item);
                    return arr;
                }, []);
                let request: any = {
                    incident_actions: [...existingActions, this.newAction],
                    adding_incident_action: true
                };
                this.processLoader = true;
                request.action = 'addIncidentAction';
                this.incidentReportService.updateIncidentReport(request, this.projectId, this.ir_row['id']).subscribe(out => {
                    this.processLoader = false;
                    if (!out.success) {
                        const message = out.message || 'Failed to add action to incident report.';
                        this.toastService.show(this.toastService.types.ERROR, message, { data: out });
                    } else {
                        const message = 'Incident action added successfully.';
                        this.toastService.show(this.toastService.types.SUCCESS, message);
                    }
                    this.initializeTable(true);
                    this.addActionModalRefGenericModal.close();
                });
            }
        });
    }

    trackByRowIndex(index: number, obj: any) {
        return index;
    }

    @ViewChild('closeOutActionHtml') private closeOutActionModalRefGenericModal: GenericModalComponent
    private closeOutActionModalHtmlRef: TemplateRef<any>;
    // @ViewChild('addActionForm') addActionForm;

    closeOutActionModal(index) {
        this.closeout_action_detail = '';
        this.closeout_action_images = [{}];
        this.incidentAction = this.ir_row['incident_actions'][index];
        this.closeOutActionModalRefGenericModal.open();
    }

    @ViewChild('closeOutDetail') closeOutDetailModel!: NgModel;
    ngAfterViewInit() {
        // Subscribe to changes AFTER the input is initialized
        if (this.closeOutDetailModel?.statusChanges) {
            this.closeOutDetailModel.statusChanges?.subscribe(() => {
                this.closeOutActionModalRefConfig.primaryBtnDisabled = !this.closeOutDetailModel.valid;
            });
        }
    }

    public closeOutActionModalRefConfig: GenericModalConfig = {
        modalTitle: 'Closeout Action',
        primaryTrailingBtnLabel: 'Close Out',
        primaryBtnDisabled: true,
        primaryTrailingBtnAction: () => {
            this.closeOutActionRequest();
            return true;
        },
        modalOptions: {
            size: 'md',
            windowClass: 'reviewForm'
        }
    }

    mediaUploadDone($event) {
        this.closeout_action_images.splice(1, 0, ...$event.userFile);
        this.closeout_action_images[0] = {};
    }

    get loading(): boolean {
        return this.newReportLoader || this.processLoader || this.editReviewLoader;
      }

    closeOutActionRequest() {
        let images = (this.closeout_action_images || []).reduce((result, elm) => {
            if (elm.id) {
                result.push(elm.id);
            }
            return result;
        }, []);

        this.incidentAction.close_out = {
            "reviewed_by": this.authUser$.first_name + ' ' + this.authUser$.last_name,
            "close_out_at": dayjs().valueOf(),
            "details": this.closeout_action_detail,
            "images": images
        }
        let projectId = this.projectId || (this.ir_row?.project_ref?.id);
        this.processLoader = true;
        this.incidentReportService.closeOutIncidentAction({ 'item_info': this.incidentAction }, projectId, this.ir_row['id']).subscribe(out => {
            this.processLoader = false;
            this.incidentAction.close_out.images = this.closeout_action_images;
            if (out.success) {
                const message = 'Incident action successfully closed.';
                this.toastService.show(this.toastService.types.SUCCESS, message);
                this.closeOutActionModalRefGenericModal.close()
                return;
            }
            const message = out.message || 'Failed to close out incident action.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        });
    }

    getActionsStatus(incidentActions = [], type) {
        if (!incidentActions.length) {
            return (type == 'html') ? '' : 'No actions assigned';
        }

        let closedOutActions = incidentActions.filter(action => action.close_out.close_out_at);
        if (closedOutActions.length == incidentActions.length) {
            return (type == 'html') ? '' : 'All actions closed out';
        }

        return (type == 'html') ? '' : `Open Actions: ${(incidentActions.length - closedOutActions.length)}/${incidentActions.length}`;
    }

    getActionsStatusText(incidentActions) {
        if (!incidentActions.length) {
            this.hasAllActionClosed = false;
            this.actionsColumnTxt = `None`;
            return;
        }
        let closedOutActions = incidentActions.filter(action => action.close_out.close_out_at);
        if (closedOutActions.length == incidentActions.length) {
            this.hasAllActionClosed = false;
            this.actionsColumnTxt = `All Closed`;
            return;
        }
        this.hasAllActionClosed = true;
        this.actionsColumnTxt = `Open: ${(incidentActions.length - closedOutActions.length)}/${incidentActions.length}`;
        return;
    }

    getPendingIncidentActions(incidentActions = []) {
        let closedOutActions = (incidentActions || []).filter(action => action.close_out.close_out_at);
        return (incidentActions.length - closedOutActions.length);
    }

    private fetchIncidentActionCatConfig(id: number) {
        this.resourceService.getCompanySettingByName('incident_report_action_category_config', id).subscribe((data: any) => {
            if (data.success && data.record && data.record.value) {
                this.actionCategories = data.record;
            } else {
                this.actionCategories = new IncidentActionCategory;
            }
            this.actionCategories.value = this.actionCategories.value.filter(cat => cat.active);
        });
    }

    openModal(content, size, windowClass = '', beforeDismiss = () => true) {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            windowClass,
            beforeDismiss
        });
    }

    setIncidentTypeMetaData() {
        this.activeStep = 0;
        let selectedIncidentType = this.incidentReport.incident_type;
        //reset report
        this.incidentReport = new ProjectIncidentReport();
        //set incident type
        this.incidentReport.incident_type = selectedIncidentType;
        this.incidentTypeMetaData = this.incidentReportMetaData[this.incidentReport.incident_type];
        this.meta_incident_type_steps = this.meta_steps_title[this.incidentReport.incident_type];
        this.formsValidationStatus = this.meta_incident_type_steps?.map(item => false);
        this.newReportConfig.modalTitle = `New Report ${(this.incidentReport.incident_type) ? '(' + this.incidentReport.incident_type + ')' : ''}`
        this.newReportConfig.hideFooter = this.incidentReport.incident_type ? false : true
        this.newReportConfig.primaryTrailingBtnLabel = 'Next'
        this.newReportConfig.secondaryTrailingBtnLabel = ''
        this.newReportConfig.showCancelTrailingBtn = true
        this.newReportConfig.primaryBtnDisabled = true
    }
    

    convertBrToNl(data: string) {
        return (data || '').split('<br>').join('\n');
    }

    @ViewChild('incidentDetailsP1') incidentDetailsP1;
    @ViewChild('newReportFormHtml') private newReportFormHtmlGenericModal: GenericModalComponent
    async newReportModal() {
        this.downloadIRLoading = true;
        this.activeStep = 0;
        this.editReportMode = false
        this.incidentReport = new ProjectIncidentReport();
        this.newReportConfig.hideFooter = true
        this.newReportConfig.bodyClass = {
            'overflow': 'unset',
            'padding': '0px'
        },
        this.incidentDetailsP1.reset()
        this.newReportConfig.modalTitle = 'New Report'
        if (this.incidentReportMetaData.length) {
            this.downloadIRLoading = false;
            return this.newReportFormHtmlGenericModal.open();
        }
        this.resourceService.getInnDexSettingByName('incident_report_meta_data_en_gb').subscribe((data) => {
            if (data.success && data.record && data.record.value) {
                this.incidentReportMetaData = data.record.value;
                this.downloadIRLoading = false;
                return this.newReportFormHtmlGenericModal.open();
            } else {
                const message = 'Something went wrong while fetching meta data.';
                return this.toastService.show(this.toastService.types.ERROR, message);
            }
        });
    }
    async editReportModal(row: ProjectIncidentReport) {
        console.log('report to be editted', row);
        this.downloadIRLoading = true;
        this.incidentReport = JSON.parse(JSON.stringify(row));
        this.incidentReport.person_affected.forEach((pa) => {
            if(!pa.contact_number) {
                pa.contact_number ={ code: this.common.default_phone_code, number: pa.contact }
            }
            pa.address  = this.convertBrToNl(pa.address);
        });
        // clearing injury_caused_by_additional for all incident type except Injury
        if(this.incidentReport.incident_type !== IncidentType.Injury && this.incidentReport.injury_caused_by_additional){
            this.incidentReport.injury_caused_by_additional = null;
        }
        if(this.incidentReport.incident_type === IncidentType.RoadTraffic && !this.incidentReport.driver_details.contact_number) {
            this.incidentReport.driver_details.contact_number = { code:  this.common.default_phone_code, number: this.incidentReport.driver_details.contact }
        }
        this.getActionsStatusText(row.incident_actions)
        await this.setIncidentDateTime();
        this.editReportMode = true;
        this.activeStep = 0;
        if (typeof this.incidentReport.weather_conditions === 'string') {
            this.incidentReport.weather_conditions = this.incidentReport.weather_conditions.length 
                ? this.incidentReport.weather_conditions.split(',') 
                : [];
        }
        
            this.incidentReport.incident_actions = this.incidentReport?.incident_actions?.map(action => ({
                ...action,
                tag_user_ref: (typeof action.tag_user_ref === 'object' && action.tag_user_ref !== null)
                ? action.tag_user_ref.id 
                : action.tag_user_ref,
                close_out: {
                    ...action.close_out,
                    images: action.close_out?.images?.length 
                        ? action.close_out.images.map(image => (image.id ? image.id : image)) 
                        : []
                }
            }));
            
        this.newReportConfig.hideFooter = true;
        (this.newReportConfig.bodyClass = {
          overflow: "unset",
          padding: "0px",
        }),
         
        this.newReportConfig.modalTitle = "Edit Incident Report";
        if (this.incidentReportMetaData.length) {
            this.downloadIRLoading = false;
          return this.newReportFormHtmlGenericModal.open();
        }
        this.resourceService
          .getInnDexSettingByName("incident_report_meta_data_en_gb")
          .subscribe((data) => {
            if (data.success && data.record && data.record.value) {
              this.incidentReportMetaData = data.record.value;
              this.incidentTypeMetaData = this.incidentReportMetaData[this.incidentReport.incident_type];
              this.meta_incident_type_steps = this.meta_steps_title[this.incidentReport.incident_type];
              this.formsValidationStatus = this.meta_incident_type_steps?.map(item => false);
              this.newReportConfig.hideFooter = this.incidentReport.incident_type ? false : true
              this.newReportConfig.primaryTrailingBtnLabel = 'Next'
              this.newReportConfig.secondaryTrailingBtnLabel = ''
              this.newReportConfig.showCancelTrailingBtn = true;
              let activeForm = this.getActiveForm();
              this.formsValidationStatus = activeForm.formsStatusArr;
              this.downloadIRLoading = false;
              return this.newReportFormHtmlGenericModal.open();
            } else {
              return alert("Something went wrong while fetching meta data.");
            }
          });
      }

    public newReportConfig: GenericModalConfig = {
        modalTitle: '',
        primaryTrailingBtnLabel: '',
        hideFooter: true,
        bodyClass: {
            'overflow': 'unset',
            'padding': '0px'
        },
        primaryTrailingBtnAction: () => {
            this.updateNewReportButtons();
            return true;
        },
        secondaryTrailingBtnLabel: '',
        secondaryTrailingBtnAction: () => {
            this.updateCancelBackButton();
            return true;
        },
        showCancelTrailingBtn:false,
        cancelTrailingBtnAction:()=>{
            if(!this.editReportMode){
                this.incidentReport.incident_type = null;
            } 
            return true;
        },
        primaryBtnDisabled: true,
        modalOptions: {
            size: 'md',
        }
    }

    @ViewChild ('injuryForm') injuryForm
    @ViewChild ('healthIncidentForm') healthIncidentForm
    @ViewChild ('roadTrafficForm') roadTrafficForm
    @ViewChild ('damageLossForm') damageLossForm
    @ViewChild ('violenceAbuseForm') violenceAbuseForm
    @ViewChild ('environmentaltForm') environmentalForm
    @ViewChild ('serviceDtrikeForm') serviceDtrikeForm
    @ViewChild ('nearMissIncidentForm') nearMissIncidentForm
    @ViewChild ('unsafeActOccurrenceForm') unsafeActOccurrenceForm
    @ViewChild ('reportForm') reportForm


    updateNewReportButtons() {
        const el = this.reportForm.nativeElement;
        el.scrollTop = 0;

        const totalSteps = this.meta_incident_type_steps.length

        if (this.activeStep < totalSteps - 1) {
            if (this.formDataValid()) {
                this.activeStep = this.formDataValid();
            }
        } else if(this.activeStep == totalSteps - 1) this.activeStep++;

        if (this.activeStep == totalSteps) {
            this.submitForm();
            this.activeStep = totalSteps - 1;
            return;
        }

        if (this.activeStep >= 0 && this.activeStep < totalSteps - 1) {
            this.newReportConfig.primaryTrailingBtnLabel = 'Next';
        } else {

            this.newReportConfig.primaryTrailingBtnLabel = this.editReportMode ? 'Save' : 'Submit';
        }

        if (this.activeStep >= 1 && this.activeStep < totalSteps) {
            this.newReportConfig.secondaryTrailingBtnLabel = 'Back';
            this.newReportConfig.showCancelTrailingBtn = false;
        } else {
            this.newReportConfig.showCancelTrailingBtn = true;
            this.newReportConfig.secondaryTrailingBtnLabel = '';
        }

    }

    async  setIncidentDateTime() {
        let incidentDateTime: dayjs.Dayjs = dayjs(+this.incidentReport.incident_date);

        this.incidentReport._incident_date = this.ngbMomentjsAdapter.dayJsToNgbDate(incidentDateTime);
        
        this.incidentReport._incident_time = {
            hour: incidentDateTime.hour(),
            minute: incidentDateTime.minute()
        };
    }

    updateCancelBackButton() {

        const totalSteps = this.meta_incident_type_steps.length
        this.activeStep = this.previousStep();

        if(this.activeStep === 0){
            this.newReportConfig.showCancelTrailingBtn = true
        }

        if (this.activeStep >= 0 && this.activeStep < totalSteps - 1) {
            this.newReportConfig.primaryTrailingBtnLabel = 'Next';
        } else {
            this.newReportConfig.primaryTrailingBtnLabel = 'Submit';
        }

        if (this.activeStep >= 1 && this.activeStep < totalSteps) {
            this.newReportConfig.secondaryTrailingBtnLabel = 'Back';
        } else {
            this.newReportConfig.showCancelTrailingBtn = true
            this.newReportConfig.secondaryTrailingBtnLabel = '';
        }
    }


    updatedActiveStep(currentStep) {
        this.activeStep = currentStep;
        const totalSteps = this.meta_incident_type_steps.length

        if (this.activeStep >= 0 && this.activeStep < totalSteps - 1) {
            this.newReportConfig.primaryTrailingBtnLabel = 'Next';
        } else {
            this.newReportConfig.primaryTrailingBtnLabel = 'Submit';
        }

        if (this.activeStep >= 1 && this.activeStep < totalSteps) {
            this.newReportConfig.secondaryTrailingBtnLabel = 'Back';
            this.newReportConfig.showCancelTrailingBtn = false;
        } else {
            this.newReportConfig.showCancelTrailingBtn = true;
            this.newReportConfig.secondaryTrailingBtnLabel = '';
        }
    }

    getActiveForm() {
        const formTypes = {
          'injury': this.injuryForm,
          'healthIncident': this.healthIncidentForm,
          'roadTraffic': this.roadTrafficForm,
          'damageLoss': this.damageLossForm,
          'violenceAbuse': this.violenceAbuseForm,
          'environmental': this.environmentalForm,
          'serviceDtrike': this.serviceDtrikeForm,
          'nearMissIncident': this.nearMissIncidentForm,
          'unsafeActOccurrence': this.unsafeActOccurrenceForm
        };

        return Object.values(formTypes).find(form => form !== undefined);
    }

    previousStep() {
        const activeForm = this.getActiveForm();
        if (activeForm) {
            return activeForm.moveToPrevious();
        }
    }

    formDataValid() {
        const activeForm = this.getActiveForm();
        if (activeForm) {
            return activeForm.moveToNext(this.activeStep + 1);
        }
    }

    submitForm() {
        const activeForm = this.getActiveForm();
        if (activeForm) {
            activeForm.submitReport();
        }
    }

      

    submitIncidentReport(request) {
        this.incidentReport.incident_date = this.incidentReport.incident_date.toString();
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle:  this.editReportMode ? 'Update Report' :`Submit Report`,
            title: `Are you sure you want to ${ this.editReportMode ? 'update' : 'submit'} this report?`,
            confirmLabel:  this.editReportMode ? 'Update' : 'Submit',
            onConfirm: () => {
                this.newReportLoader = true;
                request.finalised = true;
                if(this.editReportMode) {
                    this.incidentReport.attachment_file_ids = this.incidentReport.attachment_file_ids.map(a => ({
                        ...a,
                        file: typeof a.file === 'number' ? a.file : (a.file.id || a.file)
                    }));
                }
                request.project_ref = this.projectId;
                request.weather_conditions = (typeof  request.weather_conditions == 'object') ? ( request.weather_conditions || []).join(',') : '';
                request.site_treatment = ( request.is_onsite_treatment) ?  request.site_treatment : {};

                this.removeContactFromArray(request.witnesses);
                this.removeContactFromArray(request.person_affected ?? []);
                this.removeContactFromArray(request.thirdparty_vehicle ?? []);
                this.removeContactProperty(request.driver_details ?? {});

                this.incidentReportService.createUpdateIncidentReport( request, this.projectId).subscribe(out => {
                    this.newReportLoader = false;
                    if (!out.success) {
                        const message = out.message || 'Failed to add incident report.';
                        this.toastService.show(this.toastService.types.ERROR, message);
                        return;
                    } else {
                        const message = `Incident report ${this.editReportMode ? `updated` : 'created'} successfully.`;
                        this.toastService.show(this.toastService.types.SUCCESS, message);
                    }
                    this.initializeTable(true);
                    this.newReportFormHtmlGenericModal.close();
                    this.incidentReport.incident_type = null;
                });
            }
        });
    }

    removeContactFromArray(data) {
       data.map(obj => this.removeContactProperty(obj));
    }

    removeContactProperty(obj) {
        delete obj.contact;
    }

    moveStep(moveToStep: number, stepTitle: string, cb: any = null) {
        this.isStepDropdownOpen = false;
        this.activeStep = moveToStep
        if(this.activeStep === 0 ){
            this.newReportConfig.secondaryTrailingBtnLabel = '';
            this.newReportConfig.primaryTrailingBtnLabel = 'Next';
            this.newReportConfig.showCancelTrailingBtn = true;
        } else if(this.activeStep >= 1 && this.activeStep < this.meta_incident_type_steps.length - 1){
            this.newReportConfig.secondaryTrailingBtnLabel = 'Back';
            this.newReportConfig.primaryTrailingBtnLabel = 'Next';
            this.newReportConfig.showCancelTrailingBtn = false;
        }
        else{
            this.newReportConfig.primaryTrailingBtnLabel = this.editReportMode ? 'Save' : 'Submit';
            let activeForm = this.getActiveForm();
            activeForm.getIncidentEpoch();
        }
        this.changingFormStep.next(moveToStep);
        this.currentStepName = stepTitle;
        if (cb) {
            cb();
        }
    }

    updateFormsStatus($event) {
        this.formsValidationStatus = $event;
    }

    getEnabledStepLimit(): number {
        const firstFalseIndex = this.formsValidationStatus.findIndex(status => !status);
        return firstFalseIndex === -1 ? this.formsValidationStatus.length - 1 : firstFalseIndex; 
    }
      
    onFilterSelection(data) {
        this.filter.incidentTypes = data['incident type'].map(a => a.title);
        this.filter.projects = data.project.map(a => a.id);
        this.filter.divisions = [...data.divisions];
        this.filter.status = data.status.map(a => a.status);

        this.pageCallback({offset: 0 }, true);
    }

    searchFunction(data){
        this.filter.search = data.search;
        let stopReload = data?.stopReload;
        if(!stopReload){
            this.initializeTable(true);
        }
        this.page.pageNumber = 0;
    }

    renderFilterData(){
        return [
            {
                name:'incident type',
                list:this.incidentTypes,
                enabled:this.incidentTypes.length,
                state:false,
            },
            {
                name:'project',
                list:this.projects || [],
                enabled: (this.projects && this.projects.length) || false,
                state:false,
            },
            {
                name:'divisions',
                list:this.divisions,
                enabled:this.divisions.length,
                state:false,
            },
            {
                name:'status',
                list:this.statusMessages,
                enabled:this.statusMessages.length,
                state:false,
            },
        ];
    }

    public onActionSelection(actionVal: any) {
        const code = actionVal.code;
        this.actionButtonMetaData.isTraining = false;
        if(code === IncidentActionButtons.DASHBOARD) {
            this.openDashboardModal();
        }
    }

    openDashboardModal() {
        this.loadPowerBiComponent = true;
        if (this.isProjectPortal || this.isCompanyProjectRoute()) {
            this.biToolName = 'incident_reports';
            this.dashboardLevel = 'project';
        } else {
            this.biToolName = 'incident_reports_company';
            this.dashboardLevel = 'company';
        }
    }

    mapImages(imageArray){
        return (imageArray || []).reduce((acc, file) => {
            if (file.img_translation?.length) {
                acc.push(...file.img_translation);
            } else if (file.file_url) {
                acc.push(file.file_url);
            }
            return acc;
        }, []).map(img => ({ file_url: img }));
    }

    powerBiDashboardClose() {
        this.loadPowerBiComponent = false;
    }
}
