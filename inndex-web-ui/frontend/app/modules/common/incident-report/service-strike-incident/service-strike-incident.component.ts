import {Component, EventEmitter, Input, OnInit, Output, SimpleChang<PERSON>, TemplateRef, ViewChild} from "@angular/core";
import {ProjectIncidentReport} from "@app/core";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AppConstant} from "@env/environment";
import * as dayjs from 'dayjs';
import {Subject} from 'rxjs';
import { NumberValidationServiceService } from "@app/core/services/number-validation-service.service";
import { GenericConfirmationModalComponent } from "@app/shared";
@Component({
    selector: 'service-strike-incident',
    templateUrl: './service-strike-incident.component.html',
})
export class ServiceStrikeIncidentComponent implements OnInit {
    @Input()
    incidentReportMetaData: any = {};

    @Input()
    incidentReport: ProjectIncidentReport = new ProjectIncidentReport();

    @Input()
    incidentTypeMetaData: any = {};

    @Input()
    meta_incident_type_steps: Array<string> = [];

    @Input()
    activeStep: number = 0;

    @Input()
    projectInfo?: any;
    
    @Input()
    cb: any;

    @Input()
    formatDayJs: (param:number) => any;

    @Input()
    maxDate: (param:number) => any;

    @Input()
    changingStep: Subject<number>;

    @Output()
    formsStatus = new EventEmitter<any>();

    @Output()
    submitServiceStrikeIncidentReport = new EventEmitter<any>();

    @Output()
    setActiveStep = new EventEmitter<any>();

    @Output()
    isValidForm = new EventEmitter<any>();

    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;

    showAddWitness: boolean = false;
    witness_detail: any = {}
    witnessIndex: number = -1;

    attachments: Array<{}> = [];
    selectedAttachIndex: number = -1;

    processLoader: boolean = false;

    constructor(
        private modalService: NgbModal,
        private numberValidationService: NumberValidationServiceService
    ) {
    }

    ngOnInit() {
        this.changingStep.subscribe(v => {
            this.moveToNext(v, true);
        });
    }

    @ViewChild('incidentDetails') incidentDetails;
    @ViewChild('incidentWitnesses') incidentWitnesses;
    @ViewChild('incidentAttachment') incidentAttachment;
    @ViewChild('incidentActions') incidentActions;
    @ViewChild('incidentReview') incidentReview;
    ngDoCheck() {
        let formsStatusArr: any = [
            (this.incidentDetails && this.incidentDetails.status == 'VALID'),
            (this.incidentWitnesses && this.incidentWitnesses.status == 'VALID'),
            (this.incidentAttachment && this.incidentAttachment.status == 'VALID'),
            (this.incidentActions && this.incidentActions.status == 'VALID')
        ];

        let reviewStepStatus = formsStatusArr.every(v => v === true);
        formsStatusArr.push(reviewStepStatus);
        this.formsStatus.emit(formsStatusArr);
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['activeStep']) {
            setTimeout(()=> {
                this.subscribeToActiveFormStatus();
            }, 100)
        }

        if (changes.incidentReport && changes.incidentReport.currentValue) {
            this.attachments = [];
            this.incidentReport.attachment_file_ids.forEach((a) => a.file && this.attachments.push(a.file));
        }
    }
    
    subscribeToActiveFormStatus() {
        const activeForm = this.getFormByStep(this.activeStep + 1);
    
        if (activeForm && activeForm.form) {
            activeForm.form.statusChanges.subscribe(status => {
                const isValid = this.isValidFormForStep(activeForm, status);
                this.isValidForm.emit(isValid);
            });
    
            const isValid = this.isValidFormForStep(activeForm, activeForm.form.status);
            this.isValidForm.emit(isValid);
        }
    }
    
    isValidFormForStep(form: any, status: string): boolean {
        const step = this.activeStep + 1;
    
        if (step === 2) {
            const anyWitnesses = form.form.value.any_witnesses;
            const witnessesValid = !!this.incidentReport?.witnesses?.length && !this.showAddWitness;
            if(anyWitnesses){
                return !witnessesValid
            } else if(!anyWitnesses && status == 'VALID'){
                return false
            }
            return true;
        }
    
        return status !== 'VALID';
    }
    
    checkNumberValidation(value:string){
        const regex = /^\d+$/
        const isNumeric = regex.test(value);
        return !isNumeric || value.length == 0
    }
     
    getFormByStep(step: number) {
        switch (step) {
            case 1: return this.incidentDetails;
            case 2: return this.incidentWitnesses;
            case 3: return this.incidentAttachment;
            case 4: return this.incidentActions;
            case 5: return this.incidentReview;
            default: return null;
        }
    }

    dayjs(n: number, format?: any) {
        return dayjs(n, format).format(AppConstant.defaultDateFormat);
    }

    dateFormat(epoch) {
        return this.formatDayJs(epoch);
    }

    pad(num) {
        return (num.toString().length > 2) ? ("0"+num).slice(-3) : ("0"+num).slice(-2);
    }

    getIncidentEpoch() {
        let date = this.incidentReport._incident_date;
        let time = this.incidentReport._incident_time;
        this.incidentReport.incident_date = dayjs(`${this.pad(date.day)}/${this.pad(date.month)}/${date.year}` + ' ' + `${this.pad(time.hour)}:${this.pad(time.minute)}`, 'DD/MM/YYYY HH:mm').valueOf();
    }

    openModal(content, size, windowClass='') {
        return this.modalService.open(content, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: true,
            size: size,
            windowClass: windowClass
        });
    }

    addWitnessFormToggle(boolean) {
        this.witness_detail = {
            person_type: '',
            f_name: '',
            l_name: '',
            contact: '',
            contact_number: {code: null, number: ''},
            comments: ''
        };
        this.witnessIndex = -1;
        this.showAddWitness = boolean;
        this.subscribeToActiveFormStatus();
    }

    addWitness() {
        if (this.witnessIndex > -1) {
            this.incidentReport.witnesses[this.witnessIndex] = this.witness_detail;
        } else {
            this.incidentReport.witnesses.push(this.witness_detail);
        }
        this.showAddWitness = false;
        this.subscribeToActiveFormStatus();
    }

    removeWitness(index) {
        this.incidentReport.witnesses.splice(index, 1);
        this.showAddWitness = false; 
        this.subscribeToActiveFormStatus();
    }

    changeAnyWitnessValue() {
        if(this.incidentReport.witnesses.length){
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: 'Warning',
                title: `Are you sure you want to change witness availability? any witnesses added will be removed`,
                confirmLabel: 'Remove',
                onConfirm: () => {
                    this.incidentReport.witnesses = [];
                    this.showAddWitness = false;
                },
                onClose: () => {
                    this.incidentReport.any_witnesses = true;
                }
            });
        }
        this.showAddWitness = false;
    }

    editWitness(index) {
        this.witnessIndex = index;
        this.witness_detail = JSON.parse(JSON.stringify(this.incidentReport.witnesses[index]));
        this.showAddWitness = true;
    }

    attachmentUploadDone($event) {
        if($event && $event.userFile) {
            let attachmentsInfo = ($event.userFile || []).reduce((arr, file) => {
                let fileExist = (this.incidentReport.attachment_file_ids).find(attach => attach.file == file.id);
                if (file.id && !fileExist) {
                    arr.push({'description': '', file: file.id});
                    this.attachments.push(file);
                }
                return arr;
            }, []);
            this.incidentReport.attachment_file_ids.push(...attachmentsInfo);
            $event.userFile.length = 0
        }
    }

    removeAttachment(index) {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Remove Attachment',
            title: `Are you sure you want to remove this attachment?`,
            confirmLabel: 'Remove',
            onConfirm: () => {
                this.attachments.splice(index, 1);
                this.incidentReport.attachment_file_ids.splice(index, 1);
            }
        });
    }

    modifyDescription({ index, resetIndex }: { index: number; resetIndex: number }) {
        if (index === undefined || resetIndex === undefined) return;
        this.selectedAttachIndex = index;
        if (index > -1 && resetIndex) {
            this.incidentReport.attachment_file_ids[index].description = '';
            this.selectedAttachIndex = resetIndex;
        }
    }

    checkValidation(step){
        const formReferences = [
            this.incidentDetails,
            this.incidentWitnesses,
            this.incidentAttachment,
            this.incidentActions
        ];

        const currentForm = formReferences[step - 1];

        if (step === 2) {
            const anyWitnesses = currentForm.form.value.any_witnesses;
            const witnessesValid = this.incidentReport?.witnesses?.length && !this.showAddWitness;
            return anyWitnesses ? witnessesValid : true;
        }
        const isValid = currentForm && currentForm.form.status === 'VALID'
        
        return isValid
    }

     moveToNext(nextStep, alreadyChecked?: boolean) {

        const isValid = alreadyChecked ? alreadyChecked : this.checkValidation(nextStep)
        if(isValid){
        if (nextStep == 1) {
            this.getIncidentEpoch();
        }
        this.activeStep = nextStep;
        return this.activeStep;
    }
    }

    editForm(step){
        const editStep = this.moveToNext(step, true)
        this.setActiveStep.emit(editStep)
        this.addWitnessFormToggle(false);
     }
 
    moveToPrevious() {
        let previousStep = this.activeStep - 1;
        this.activeStep = previousStep;
        return this.activeStep;
    }

    submitReport() {
        this.submitServiceStrikeIncidentReport.emit(this.incidentReport);
    }
}
