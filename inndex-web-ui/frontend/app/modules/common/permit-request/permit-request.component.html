<div [ngClass]="{'d-flex': !isProjectPortal}" >
    <company-side-nav *ngIf="!isProjectPortal" [employer]="employer" [companyResolverResponse]="companyResolverResponse" (projectRetrieved)="projectRetrieved($event)"></company-side-nav>
    <div [ngClass]="{'col-12 px-4': isProjectPortal, 'w-100 px-3 detail-page-header-margin': !isProjectPortal, 'ml-fix': (!isMobileDevice && !isProjectPortal)}">
        <div class="row">
            <project-header [projectData]="projectInfo" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header>
            <div [ngClass]="{'col-12 my-3': true, 'd-none': pageLoading}">
                <div class="col-12 pt-2 outer-border-radius ngx-datatable-custom">
                    <div class="d-flex flex-wrap flex-column flex-sm-row justify-content-between mb-2">
                        <h5 class="float-md-left">Total {{ permitPhrase }} <small>({{ page.totalElements }})</small></h5>
                        <action-button
                                [actionList]="actionButtonMetaData.actionList"
                                (selectedActionEmmiter)="onActionSelection($event)"
                                [hideNewFeatureBtn]="true">
                        </action-button>
                    </div>
                    <div class="clearfix"></div>
                    <search-with-filters (searchEmitter)="searchFunction($event)" (filterEmitter)="onFilterSelection($event)" [filterData]='filterData'></search-with-filters>

                    <ngx-datatable
                        class="bootstrap table table-v4 ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
                        [scrollbarV]="true"
                        [virtualization]="false"
                        [loadingIndicator]="loadingInlinePermitReq"
                        [columnMode]="'force'"
                        [footerHeight]="40"
                        [headerHeight]="40"
                        [rowHeight]="'auto'"
                        [rows]="permitRequests"
                        [externalPaging]="true"
                        [columns]="columns"
                        [count]="page.totalElements"
                        [offset]="page.pageNumber"
                        [limit]="page.size"
                        (page)="pageCallback($event)"
                    >
                        <ng-template #recordIdTmpl let-row="row">
                            <div style="display: inline-grid; line-height: 1.625;">
                                <span>{{ row.record_id }}</span>
                                <span class="visibility-hidden">...</span>
                            </div>
                        </ng-template>

                        <ng-template #permitTypeTmpl let-row="row">
                            <div style="display: inline-grid; line-height: 1.625;">
                                <span>{{row.permit_ref.ref_number}} - {{ row.permit_ref.permit_type }}</span>
                                <span class="visibility-hidden">...</span>
                            </div>
                        </ng-template>

                        <ng-template #requestedByTmpl let-row="row">
                            <div style="display: inline-grid; line-height: 1.625;">
                                <span>{{ row?.requestor_ref?.first_name }} {{ row?.requestor_ref?.last_name }}</span>
                                <!-- todo:vshal - it should be pull from dedicated column requestor_company-->
                                <span style="font-size: 11px;">({{row?.requestor_ref?.user_induction_info?.user_employer}})</span>
                            </div>
                        </ng-template>

                        <ng-template #dateRequestedTmpl let-value="value">
                            <div style="display: inline-grid;  line-height: 1.625;">
                                <span>{{ dayjs(+value).format(defaultDateFormat)}}</span>
                                <span style="font-size: 11px;">({{dayjs(+value).format(defaultTimeFormat)}})</span>
                            </div>
                        </ng-template>

                        <ng-template #startOnDateTimeTmpl let-value="value">
                            <div style="display: inline-grid;  line-height: 1.625;">
                                <span>{{ dayjs(+value).format(defaultDateFormat)}}</span>
                                <span style="font-size: 11px;">({{dayjs(+value).format(defaultTimeFormat)}})</span>
                            </div>
                        </ng-template>

                        <ng-template #locationTmpl let-row="row">
                            <div style="display: inline-grid;  line-height: 1.625;">
                                <span>{{ getTagsValue(row.tags, 'location') }}</span>
                            </div>
                        </ng-template>

                        <ng-template #statusLabelTmpl let-row="row">
                            <span [ngStyle]="{color: row.status_meta.color}"></span>

                            <div class="d-inline-block">
                                <span class="btn btn-sm btn-outline-primary text-white" [ngStyle]="{background: row.status_meta.color, 'border-color': 'transparent', 'border-radius': '5px'}">{{ row.status_meta.label }}</span>
                            </div>
                        </ng-template>

                        <ng-template #actionTmpl let-row="row" let-value="value">
                            <div style="line-height: 41px;">
                                <button
                                    title="Preview Pdf" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1"
                                    (click)="viewDownloadPermitPdf(row)">
                                    <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.search_svg" alt="">
                                </button>
                                <button
                                    title="Download PDF" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1"
                                    (click)="viewDownloadPermitPdf(row, 'download')">
                                    <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.download" alt="">
                                </button>
                                <button title="Audit Trail" type="button" class="btn btn-sm align-middle btn-action mr-1 mb-1 p-1"
                                        (click)="viewAuditTrail(row)">
                                    <img class="img-icon px-1" [src]="AssetsUrlSiteAdmin.declarations" alt="">
                                </button>
                                <button title="Share {{ permitSglrPhrase }} Details"
                                        class="btn btn-sm align-middle btn-action mr-1 mb-1"
                                        (click)="openSharePermitModal(row)">
                                    <i class="fas fa-share-square"></i>
                                </button>
                            </div>
                        </ng-template>
                    </ngx-datatable>
                </div>
            </div>
        </div>
    </div>
</div>
<block-loader [show]="(processingLoader || pageLoading)" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>

<ng-template #auditTrailModal let-d="dismiss" let-c="close">
    <div class="modal-header modal-head-v2">
        <span class="modal-title fw-500">Status: {{ this.permitRequest.status_meta.label }}</span>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body modal-scroll py-4 px-5">
        <div class="w-100 mb-2 x-large-font font-weight-bold">Signature Status</div>
        <ng-container *ngFor="let signature of permitRequest.permit_ref.signatures trackBy : trackByRowIndex; let i = index;">
            <div class="list-group" *ngIf="!signature.is_requestor && !signature.is_closeout_requestor && !signature.is_closeout">
                <span class="flex-column align-items-start">
                    <div class="d-flex w-100 justify-content-between">
                        <span class="mb-1 medium-font d-flex align-items-center">
                            <svg *ngIf="permitRequest.signatures[i] && permitRequest.signatures[i].signatory_user_ref else alertSvg" width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><rect width="15" height="14.8295" fill="url(#pattern0)"/><defs><pattern id="pattern0" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_15351_1553" transform="scale(0.0113636 0.0114943)"/></pattern><image id="image0_15351_1553" width="88" height="87" xlink:href="data:image/png;base64,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"/></defs></svg>
                            <ng-template #alertSvg>
                                <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><rect width="15" height="15" fill="url(#pattern1)"/><defs><pattern id="pattern1" patternContentUnits="objectBoundingBox" width="1" height="1"><use xlink:href="#image0_15351_1558" transform="scale(0.011236)"/></pattern><image id="image0_15351_1558" width="89" height="89" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAFkAAABZCAYAAABVC4ivAAAACXBIWXMAAC4jAAAuIwF4pT92AAAFaklEQVR4nO2dMYvcVhDHx8KHCBjfFQqGILj0Dj73gWTrFPH5E9weSeHCxlelC9jgKo3X32A3KZLyLkWaNLuGpLAL35m0Ibe2GoMwt4E4CDu5MGKkSLt6K+npPWne7vvBcl6dT3r732H03mjezIXz83PgSOB61wFgCwCSnyKOAeAMAE79KDzl+FlYiBy4Xg8AeiQovrYbnG6CggPAGF8chO9E5MD10DJ36YXibmq83BQADgFg6EfhscbrCGlVZLLYPomrU1gRKPiQBG/NwlsROXA9FPYAAHa0X6w6IwAYtGHdWkUmce819LG6QR/e12nZWkSmmcEAAD5VfnJ9PEKD8KPwTPUVlIpMNzS03LvKTtouM7LqQ5VXVSYyWe8hc9dQlSMSW4lVOypOErge3tSerYjAyA1c5JDhNKaRJZN7QN+7p+rTMWTfj8Jhk2FJi0wCj5lNy3Rx34/Ce7LnlhJ5zQROGPlR2Jf5w9o+eU0FRvYC15NyG7VEXmOBE6SEriyyFThlj2ZTlaljyQMrcMrDwPV2q/7nSiLTN7fK0zQZhlXn0aWzCzrRs44+CHdOMB5etjJcasnkh5Wu41eMHYrVLKXMXXAPU3LgLj2MECJ0F9ZN1GLqR+GHoj9YZsmDbsdtFNuB6wndRqHI9ESDTcDd2dqEK08n4Edh+sL3eJwRB3QPWxy/YIzSwRAdbFz7CDauXc2dGd/jcUZs0nPMcpHJiu3NTo5Cay6y5FpLRkuOQmvOiUxTEbt0bsZCOHTekqXipZYc2/NxjVTkTOqURbE1Zy25q9SpVeRG9gY4L7JFHb0ikZeuvy21SY02FplmFdZVqGXBkpUkceji7fPfOA9PBM4y4qBRIjJrV/Hv2YzBKKSIjdcISzaYnMg2VqGH2EM4qpLqLIXEc2WnZPuWpRk7icjWkjVjLVkzOI1TkgTeFdHjX0wYptkim4IVuQWsyC1gRW4BK7J+jh2qF2HRBGZ8OlSQw6IRhwpwWPSAm+PB4VpSZp433/2QOzL/nimxthdpbBPuO/pff3kb/vr2+/S9Iau9nMinJpRNMETYLLhbLJ3CjVkMafWIZ25WZH2cJBt2YpHp5jddzc/aGanhZld87Hc5YWa9+8nHsLHDKvlbRLo9OCtyo5oOurn89Vfwwavf4f2fj+DKkzHH7QxZptkqXanIdJCly0AxL925lTuG2xne+/yzzsZUQs4rzAeIWFoziulsXl44Pi88I3I7x4wQWZRB9G76ovWxVGAyv4rOiUy/HDEbNPz940/wz4uXC8ffZFaAjFgw1KJ4MstNkq+/uA3R41/jf6Pgfz74JhafGdOiolCF234D1xsbVp2QC4WVt0QiY8rnH2skjgpwhVeYKFT4+Il88yPun4oZwv2Py6oEbFF0zmbgl3PkR6Fwz43wQSoFN+y+vnJmZTotfVpN1VeP2h2zcZQWTq2SEtC3ETohoyplgEtFpm/J7vFb5KTqZv9KyS0UPNpXO0ajQT+8W7W+cuUMIppk319LSfPMqExZ5af8tavOUq3KdS7Ed7NuOfbauXBU3pZdEKkl9mXq3UslHK6h0DOyYKlQcNNy63h3fSh9AjNIfLB0Ymaj1Fk/CjEsepMGsorgNO160646jfOTyUf1aECrxKjuLEKEyj4jpjdySVDe0EV5WyKqnTE0dL+20iYuCdoabFHtSrwxmhAqjZfIfhRqSVfT3cUMXcgBY7Gn1FRL61P6tvrxJWJzKVE5ocaHraRAtN6+kwrT9am/UpskbTwHbe8u6KwRLVl3L9MnVYeFn1B2ZWf9UYFLt1/4/wl50u23R9UL6tQNTbr8Jp1+j3U0MJSBbd/qLPQFFJUzZyOkEAD4D9Hw64BaWXijAAAAAElFTkSuQmCC"/></defs></svg>
                            </ng-template>
                            <span class="ml-2 font-weight-bold">
                                {{ signature.field_label }}
                            </span>
                        </span>
                    </div>
                    <ng-container *ngIf="permitRequest.signatures[i] && permitRequest.signatures[i].signatory_user_ref">
                        <p class="mb-1 medium-font ml-4">{{permitRequest.signatures[i]?.signatory_user_ref?.user_name}} ({{permitRequest.signatures[i]?.signatory_user_ref?.user_employer}})</p>
                        <small class="medium-font ml-4" style="color: var(--spanish-gray)">
                            Date Signed: {{dayjs(permitRequest.signatures[i]?.date_signed).format(defaultDateFormat)}} {{dayjs(permitRequest.signatures[i]?.date_signed).format(timFormatWithoutSecs)}}
                        </small>
                    </ng-container>
                </span>
            </div>
            <hr class="my-2" *ngIf="!signature.is_requestor && !signature.is_closeout_requestor && !signature.is_closeout">
        </ng-container>

        <div class="w-100 mb-2 mt-4 x-large-font font-weight-bold">Audit Trail</div>

        <table class="table table-bordered rounded-corners">
            <tbody>
            <tr style="background: var(--cultured);">
                <th class="py-2" style="width: 15%">Date</th>
                <th class="py-2" style="width: 15%">Status</th>
                <th class="py-2" style="width: 30%">Author</th>
                <th class="py-2" style="width: 40%">Details</th>
            </tr>
            <tr *ngFor="let status_log of permitRequest.status_logs trackBy : trackByRowIndex; let i = index;">
                <td class="py-2">
                    <div style="display: inline-grid;">
                        <span>{{ dayjs(+status_log.timestamp).format(defaultDateFormat)}}</span>
                        <small class="small-font" style="color: var(--spanish-gray)">({{dayjs(+status_log.timestamp).format(defaultTimeFormat)}})</small>
                    </div>
                </td>
                <td class="py-2">
                    <span class="mb-1 medium-font font-weight-bold" [ngStyle]="{'color': getLogMetaInfo(status_log, 'color')}">{{status_log.note}}</span>
                </td>
                <td class="py-2">
                    {{status_log?.name}}
                </td>
                <td class="py-2">
                    {{status_log?.comment}}
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="modal-footer py-1">
        <button type="button" class="btn btn-brandeis-blue" (click)="d('Cross click')">Done</button>
    </div>
</ng-template>

<i-modal #previewPdfModalRef [title]="permitRequest.permit_ref?.permit_type" (onCancel)="closeModal()" [showFooter]="false" size="lg" [showCancel]="false">
    <div *ngIf="showModal" class="overflow-hidden pdf-viewer" style="height: calc(100vh - 142px)">
        <ng2-pdfjs-viewer
            [fullScreen]="true"
            [pdfSrc]="fillablePdf.file_url"
            [print]="false"
            [page]="1"
            viewerId="pdfFliper"
            [openFile]="false"
            [viewBookmark]="false"
            [showSpinner]="true"
        >
        </ng2-pdfjs-viewer>
    </div>
</i-modal>

<i-modal #previewPdfDownloadModalRef [title]="downloadPDFModalTitle" (onCancel)="closeModal()" (onClickRightPB)="downloadPermitPdf()" [rightPrimaryBtnTxt]="'Download'">
    <div *ngIf="showModal" class="overflow-hidden pdf-viewer p-2">
        <div class="d-flex justify-content-between light-grey-bottom-border" *ngIf="permitRequest.has_register">
            <div>Include <span i18n="@@operative">Operative</span> Register</div>
            <div class="custom-control custom-switch d-inline-block">
                <input type="checkbox" class="custom-control-input" id="register" name="register"
                    [checked]="isRegisterChecked" (change)="isRegisterChecked = !isRegisterChecked">
                <label class="custom-control-label" for="register"></label>
            </div>
        </div>
        <div class="d-flex justify-content-between light-grey-bottom-border mt-3"
            *ngIf="permitRequest.mandatory_attachments.length > 0">
            <div>Include Full Size Attachments</div>
            <div class="custom-control custom-switch d-inline-block">
                <input type="checkbox" class="custom-control-input" id="attachment" name="attachment"
                    [checked]="isAttachmentChecked" (change)="isAttachmentChecked = !isAttachmentChecked">
                <label class="custom-control-label" for="attachment"></label>
            </div>
        </div>
    </div>
</i-modal>
<share-tool-report-to-email
    [projectId]="projectId" [employerId]="employerId"  #sharePermitModal
    [tool_name]="'Share '+permitSglrPhrase" (onSave)="sharePermit($event)">
</share-tool-report-to-email>
<power-bi-dashboard *ngIf="loadPowerBiComponent" (powerBiDashboardClose)="powerBiDashboardClose()" [modalTitle]="biModalTitle" [toolLabel]="biToolLabel" [toolName]="biToolName"></power-bi-dashboard>
