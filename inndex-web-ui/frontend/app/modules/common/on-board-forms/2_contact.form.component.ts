import {Component, ElementRef, EventEmitter, Input, NgZone, OnChanges, OnInit, Output, ViewChild} from "@angular/core";
import {AuthService, ContactDetail, FeatureExclusionUtility, UserService} from "@app/core";
import {concat, of, Subject} from "rxjs";
import {NavigationStart, Router} from "@angular/router";
import {catchError, debounceTime, distinctUntilChanged, filter, switchMap, takeUntil, tap} from "rxjs/operators";
import {MapsAPILoader} from "@agm/core";
import { GenericConfirmationModalComponent } from "@app/shared";
import { NgForm } from "@angular/forms";
// declare var google: any;
declare const google: any;
// import { } from 'googlemaps';

@Component({
    selector: 'on-board-contact-detail-form',
    templateUrl: './2_contact.form.component.html',
})
export class OnBoardContactFormComponent implements OnInit, OnChanges {

    @Output()
    onSave: any = new EventEmitter<any>();

    @Output()
    back: any = new EventEmitter<any>();

    @Output()
    changed: any = new EventEmitter<boolean>();

    @Input()
    contact_detail: ContactDetail = new ContactDetail();

    @Input()
    country_code: string;

    @Input()
    isProcessing: boolean = false;

    @Input()
    title: string;

    selectedCountryCode: string;
    disableNextBtn: boolean = true;
    postcodes$: any;
    postCodeSearchInput$: Subject<string> = new Subject<string>();
    postCodeLoading = false;

    isAlternateFlow: boolean = true;
    countryRestrictionAdded: boolean = false;

    emergencyContactNameIsRequired: boolean = true;
    emergencyContactNoIsRequired: boolean = true;
    countries: any[] = [];
    @ViewChild("addressForm") addressForm: NgForm;
    @ViewChild("changeConfirmationModalRef")
    private changeConfirmationModalRef: GenericConfirmationModalComponent;
    initialFormValue;
    onDestroy$: Subject<void> = new Subject();
    formValueChanged: boolean = false;
    targetUrl: string;
    postcodeInput: any = {};
    loading = false;

    constructor(
        private router: Router,
        private authService: AuthService,
        private featureExclusionUtility: FeatureExclusionUtility,
        private userService: UserService,
        private mapLoader: MapsAPILoader,
        private ngZone: NgZone,
    ) {
        this.router.events
      .pipe(
        takeUntil(this.onDestroy$),
        filter((event) => event instanceof NavigationStart)
      )
      .subscribe((event: NavigationStart) => {
        this.targetUrl = event.url;
        if (
          JSON.stringify(this.initialFormValue) !=
          JSON.stringify(this.contact_detail)
        ) {
          this.userService.formValueChanged.next(true);
          this.changeConfirmationModalRef.openConfirmationPopup({
            headerTitle: "Warning",
            title:
              "You have unsaved changes. Do you want to save them before leaving this page?",
            leadingBtnLabel: "Cancel",
            confirmLabel: "Save",
            cancelLabel: "No",
            borderCancelButton: true,
            hasCancel: true,
          });
        } else {
          this.userService.formValueChanged.next(false);
        }
      });
    }
    ngOnInit() {
        this.emergencyContactNameIsRequired = this.featureExclusionUtility.emergencyContactNameIsRequired(this.country_code);
        this.emergencyContactNoIsRequired = this.featureExclusionUtility.emergencyContactNoIsRequired(this.country_code);
        this.getCountries();
        this.setDisableNext();
    }

    ngOnChanges() {
        if(!this.contact_detail.street){
            this.contact_detail.street = null;
        }
        setTimeout(() => {
            this.initialFormValue = JSON.parse(JSON.stringify(this.contact_detail));
        }, 0);
        if(!this.contact_detail.country){
            return;
        }
        this.onCountrySelection({}, false);
        // need to pass country code here, so that default form can be initialized
        setTimeout(() => { this.addCountryRestrictionAfterInit()}, 0);
    }

    initPostCodeSearch() {
        this.postcodes$ = concat(
            of([]),
            this.postCodeSearchInput$.pipe(
                debounceTime(200),
                filter(term => !!term),
                distinctUntilChanged(),
                tap(() => this.postCodeLoading = true),
                switchMap(term => this.userService.callPostCodeLookup(term).pipe(
                    catchError(() => of([])),
                    tap(() => this.postCodeLoading = false)
                ))
            )
        );
    }


    setSelctedAddress($event) {
        this.changed.emit(true);
        if(!$event){
            // User Removed selection
            this.contact_detail.post_code = undefined;
            return true;
        }
        this.contact_detail.street = this.getStreet($event);
        this.contact_detail.city = $event.post_town;
        this.contact_detail.post_code = $event.postcode;
    }

    blockSpecialChars(event) {
        // allowed key ranges
        // without shift 0 to 9 keys are codes 48 to 57
        // A to Z keys are codes 65 to 90
        // number pad are codes 96 to 105
        const k = event.charCode;
        return((k > 64 && k < 91) || (k > 96 && k < 123) || k == 8 || k == 32 || (k >= 48 && k <= 57)); 
    }
/*
    private getHouseNameNumber(address: any) {
        return (undefined != address.premise) ? address.premise : '';
    }*/

    private getStreet(address: any) {
        let streetArr = [];
        streetArr.push((undefined != address.line_1) ? address.line_1 : '');
        streetArr.push((undefined != address.line_2) ? address.line_2 : '');
        streetArr.push((undefined != address.line_3) ? address.line_3 : '');
        return streetArr.filter(Boolean).join(', ');
    }

    getAddressLine(address: any) {
        let addressComponents = [];
        // addressComponents.push(this.getHouseNameNumber(address));
        addressComponents.push(this.getStreet(address));
        addressComponents.push(address.post_town);
        addressComponents.push(address.postcode);
        return addressComponents.filter(Boolean).join(', ');
    }


    save(form: any, targetUrl?) {
        if (!form.valid) {
            return false;
        }
        this.onSave.emit({ contact_detail: this.contact_detail, targetUrl });
    }

    getCountries() {
        this.loading = true
        this.userService.getCountries('countries').subscribe((res: any) => {
            this.countries = res;
            this.loading = false;
            // this.countriesBuffer = this.countries.slice(0, this.bufferSize);
            this.addCountryRestrictionAfterInit();
        });
    }

    private addCountryRestrictionAfterInit(){
        if(!this.countryRestrictionAdded && this.contact_detail.country && (this.countries || []).length){
            let code = ((this.countries || []).find(c => c.name === this.contact_detail.country) || {}).code;
            console.log('Defining country code to ', code);
            this.onCountrySelection({code}, false);
        }
    }

    onCountrySelection($event: any = {}, resetOnChange = true){
        this.selectedCountryCode = $event.code
        if($event.code){
            this.postcodeInput = this.featureExclusionUtility.showProfilePostalCode($event.code);
        }
        if(resetOnChange){
            // reset address inputs
            this.contact_detail = {
                ...this.contact_detail,
                street: null,
                city: null,
                post_code: null,
            };
        }
        // setTimeout(() => {
        //     this.initialFormValue = JSON.parse(JSON.stringify(this.contact_detail));
        // }, 0);
        if(!this.contact_detail.country){
            return false;
        }
        if(['United Kingdom', 'United Kingdom of Great Britain and Northern Ireland'].includes(this.contact_detail.country)){
            // UK specific flow
            this.isAlternateFlow = false;
            this.countryRestrictionAdded = true;
            return this.initPostCodeSearch();
        }
        // AUS specific flow
        // if(['Australia', 'United States of America'].includes(this.contact_detail.country)){
        this.isAlternateFlow = true;
        setTimeout(() => {
            this.findAddress($event.code);
        }, 10);
        return ;
        // }
        // this.isAlternateFlow = false;
        // this.initPostCodeSearch();
    }

    @ViewChild('search') searchElementRef: ElementRef;

    findAddress(countryCode){
        if(this.searchElementRef && this.searchElementRef.nativeElement){
            this.mapLoader.load().then(() => {
                google.maps.event.addDomListener(this.searchElementRef?.nativeElement, 'keydown', function (e) {
                    if (e.keyCode == 13) {
                        if (e.preventDefault) {
                            e.preventDefault();
                        } else {
                            // IE
                            e.cancelBubble = true;
                            e.returnValue = false;
                        }
                    }
                });
                let autocomplete = new google.maps.places.Autocomplete(this.searchElementRef.nativeElement, { types: ["address"] });
                if(countryCode){
                    autocomplete.setComponentRestrictions({ country: [countryCode], });  // Lookout into Specific country only
                    this.countryRestrictionAdded = true;
                }else{
                    // fallback to UK
                    autocomplete.setComponentRestrictions({ country: ['GB'], });
                }
                autocomplete.setFields(["address_component", /*'formatted_address'*/]);
                if ("geolocation" in navigator) {
                    navigator.geolocation.getCurrentPosition((position) => {
                        // console.log('got user location', position);
                        const geolocation = {
                            lat: position.coords.latitude,
                            lng: position.coords.longitude,
                        };
                        const circle = new google.maps.Circle({
                            center: geolocation,
                            radius: position.coords.accuracy,
                        });
                        autocomplete.setBounds(circle.getBounds());
                    }, error => {
                        console.log('Failed while requesting location', error);
                    });
                }
                autocomplete.addListener("place_changed", () => {
                    this.ngZone.run(() => {
                        // some details
                        let place/*: google.maps.places.PlaceResult*/ = autocomplete.getPlace();
                        let addressLine = (place.address_components || []).filter((add: any) => !(add.types.includes('country') || add.types.includes('postal_code'))).map(a => a.long_name).join(', ');
                        let city = (place.address_components || []).filter((add: any) => add.types.includes('locality') || add.types.includes('administrative_area_level_3')).map(a => a.long_name).join(', ');
                        let postal_code = (place.address_components || []).filter((add: any) => add.types.includes('postal_code') || add.types.includes('post_box')).map(a => a.long_name).join(', ');

                        let full = {
                            address : addressLine,
                            address_components : place.address_components,
                            city,
                            zip_code : postal_code,
                        };
                        console.log('compact detail', full, this.searchElementRef.nativeElement.value);
                        // console.log('Full line', addressLine || this.searchElementRef.nativeElement.value);
                        // console.log('city', city);
                        this.contact_detail.street = addressLine || this.searchElementRef.nativeElement.value;
                        this.contact_detail.city = city;
                        this.contact_detail.post_code = postal_code;
                        this.changed.emit(true);
                    });
                });
            });
        }
    }

    goBack() {
        this.back.emit();
    }

    onNumberInput($event, name) {
        this.contact_detail[name] = $event;
        this.onInputChanged($event);
    }

    onInputChanged($event){
        this.changed.emit(true);
        this.setDisableNext();
    }

    setDisableNext() {
        const homeNumber = this.contact_detail['home_number']?.number;
        const mobileNumber = this.contact_detail['mobile_number']?.number;
        const emergencyContactNumber = this.contact_detail['emergency_contact_number']?.number;
    
        const anyNumberInvalid = [homeNumber, mobileNumber, emergencyContactNumber].some(number =>
            number && !this.containsOnlyNumbers(number)
        );
    
        const mobileNumberNotProvided = !mobileNumber || mobileNumber?.length === 0;
        const emergencyContactNumberNotProvided = this.emergencyContactNoIsRequired && (!emergencyContactNumber || emergencyContactNumber?.length === 0);
    
        this.disableNextBtn = anyNumberInvalid || mobileNumberNotProvided || emergencyContactNumberNotProvided;
    }
    
    containsOnlyNumbers(inputString) {
        const regex = /[^0-9]/;
    
        return !regex.test(inputString);
    }

    confirmationVal(confirmVal) {
        if (confirmVal.buttonLabel === "Save") {
          this.save(this.addressForm, this.targetUrl);
        } else if (confirmVal.buttonLabel === "No") {
          this.contact_detail = { ...this.initialFormValue };
          this.router.navigate([this.targetUrl]);
        }
      }

    get numberPickerInitial() {
        return JSON.parse(JSON.stringify(this.contact_detail));;
    }
    
      ngOnDestroy() {
        this.onDestroy$.next();
        this.onDestroy$.complete();
        this.userService.formValueChanged.next(false);
      }
}
