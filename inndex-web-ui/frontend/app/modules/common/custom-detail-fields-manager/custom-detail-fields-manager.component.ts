import {Component, OnInit, TemplateRef, Input, ViewChild, Output, EventEmitter, ViewChildren, QueryList, ChangeDetectorRef, SimpleChanges, NgZone} from '@angular/core';
import { NgModel } from '@angular/forms';
import { IModalComponent } from '@app/shared';
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import { take } from 'rxjs/operators';

@Component({
    selector: 'custom-detail-fields-manager',
    templateUrl: 'custom-detail-fields-manager.component.html',
})
export class CustomDetailFieldsManagerComponent implements OnInit {

    @Input()
    container_obj;

    @Input()
    fields_key: string;

    fieldIndex: number = 0;
    fieldTypes: Array<object> = [{'label': 'Textbox', 'key': 'textbox'}, {'label': 'Multiline Textbox', 'key': 'textarea'}, {'label': 'Dropdown', 'key': 'dropdown'}];

    @Output()
    validityChanged: any = new EventEmitter<any>();
    @ViewChildren('fieldItem') fieldInputs: QueryList<NgModel>;
    @ViewChildren('fieldType') fieldInputTypses: QueryList<NgModel>;
    @ViewChildren('optionLabel') optionLabel: QueryList<NgModel>;

    @Input() openDetailModal?: boolean = false;

    constructor(private modalService: NgbModal,
            private cdRef: ChangeDetectorRef,
            private ngZone: NgZone) {}

    ngOnInit() {
        this.validityChanged.emit(this.checkFieldValidity());
    }

    ngOnChanges(change: SimpleChanges) {
        if(change['openDetailModal']) {
            this.markTouchedAndValidate();
        }
    }

    markTouchedAndValidate() {
        this.fieldInputs?.forEach(input => {
            input.control?.markAsTouched();
        });
        this.fieldInputTypses?.forEach(input => {
            input.control?.markAsTouched();
        });
        this.validityChanged.emit(this.checkFieldValidity());
    }

    addFieldBlock() {
        if (!this.container_obj[this.fields_key]) {
            this.container_obj[this.fields_key] = [];
        }
        this.container_obj[this.fields_key].push({
            field: "",
            is_mandatory: false,
            field_type: "",
            options: []
        });
        this.validityChanged.emit(this.checkFieldValidity());
    }

    removeFieldBlock(index) {
        this.container_obj[this.fields_key].splice(index, 1);
        this.cdRef.detectChanges();
        this.validityChanged.emit(this.checkFieldValidity());
    }

    inputChanged(){
        this.cdRef.detectChanges();
        this.validityChanged.emit(this.checkFieldValidity());
    }

    checkFieldValidity() {
        const customFields = this.container_obj[this.fields_key] || [];

        // If no custom fields have been added, consider it valid
        if (customFields.length === 0) {
            return true;
        }

        // If custom fields exist, validate them
        const allValidInputs = this.fieldInputs?.toArray().every(input => input.valid);
        const allValidTypes = this.fieldInputTypses?.toArray().every(input => input.valid);
        const noEmpty = customFields.every(f => (f.field || '').trim() !== '' && f.field_type !== '')
        return allValidInputs && allValidTypes && noEmpty;
    }

    @ViewChild('dropdownOptionsModal')
    private dropdownOptionsModalRef: IModalComponent;
    manageOptions(i) {
        this.fieldIndex = i;
        if(this.container_obj[this.fields_key][this.fieldIndex] && !this.container_obj[this.fields_key][this.fieldIndex].options) {
            this.container_obj[this.fields_key][this.fieldIndex].options = [{
                'label': '',
                "is_active": true,
            }]
        } else {
            this.container_obj[this.fields_key][this.fieldIndex].options = (this.container_obj[this.fields_key][this.fieldIndex].options || []).filter(option => option.label);
            this.container_obj[this.fields_key][this.fieldIndex].options = [
                {
                    'label': '',
                    "is_active": true,
                },
                ...(this.container_obj[this.fields_key][this.fieldIndex].options)
            ]
        }
        this.dropdownOptionsModalRef.open();
        this.checkInputValidation();
    }

    checkInputValidation() {
         this.ngZone.onStable.pipe(take(1)).subscribe(() => {
            this.optionLabel.forEach((input, i) => {
                if(i !== 0) input.control?.markAsTouched();
            });
        });
    }

    removeOptionRow(j: number):void {
        this.container_obj[this.fields_key][this.fieldIndex]['options'].splice(j, 1);
        this.optionLabel?.toArray()?.splice(j, 1);
        this.cdRef.detectChanges()
    }

    addOption(j) {
        if(this.container_obj[this.fields_key][this.fieldIndex]['options'] && this.container_obj[this.fields_key][this.fieldIndex]) {
            this.container_obj[this.fields_key][this.fieldIndex]['options'] = [
                {
                    "label": '',
                    "is_active": true,
                },
                ...(this.container_obj[this.fields_key][this.fieldIndex]['options'].sort(function(a, b) {
                    return a.label.toLowerCase() < b.label.toLowerCase() ? -1 : a.label.toLowerCase() > b.label.toLowerCase() ? 1 : 0;
                }))
            ];
        }
    }

   updateOptions() {
        let options = this.container_obj[this.fields_key][this.fieldIndex]?.options || [];
        options = options.slice(1);

        this.container_obj[this.fields_key][this.fieldIndex].options = options
            .filter(option => option.label?.trim())
            .sort((a, b) => a.label.toLowerCase().localeCompare(b.label.toLowerCase()));

        if (this.container_obj[this.fields_key][this.fieldIndex].options.length === 0) {
            this.removeFieldBlock(this.fieldIndex);
        }

        this.dropdownOptionsModalRef.close();
    }

    trackByRowIndex(index: number, obj: any) {
        return index;
    }

    fieldTypChanged(i, fieldType){
        if(fieldType == 'dropdown') {
            this.manageOptions(i)
        } else {
            if(this.container_obj[this.fields_key] && this.container_obj[this.fields_key][i] && this.container_obj[this.fields_key][i].options?.length)
                this.container_obj[this.fields_key][i].options = []
        }
        this.validityChanged.emit(this.checkFieldValidity());
    }

    closeOptionsPopup(d){
        if(this.container_obj[this.fields_key][this.fieldIndex] && (this.container_obj[this.fields_key][this.fieldIndex].options.length == 1)) {
            this.container_obj[this.fields_key][this.fieldIndex].field_type = '';
            this.container_obj[this.fields_key][this.fieldIndex].options = [];
        }
        this.updateOptions();
        this.validityChanged.emit(this.checkFieldValidity());
        
        this.dropdownOptionsModalRef.close();
    } 

    trackByIndex(index: number): number {
        return index;
    }
    
    hasValidOptions(): boolean {
        const field = this.container_obj?.[this.fields_key]?.[this.fieldIndex];
        if (!field || !Array.isArray(field.options)) return false;

        return field.options.some(opt => (opt.label || '').trim().length > 0);
    }

}
