<div class="mb-2 mx-3 flex-wrap gap-8 d-flex">
    <div class="col-md-5 d-inline-block p-0">
        <h5 class="float-md-left col-md-12 p-0">Total On-site Vehicles <small> ({{onSiteVehicleCount}}) </small></h5>
    </div>
</div>

<div class="col-md-12 mb-3 p-0">
    <div class="row w-100 p-0 text-center">
        <div class="col-12 col-md-6 tablet-full-width">
            <div class="ml-md-3" *ngIf="taggedOwners.length">
                <search-with-filters (searchEmitter)='searchFunction($event)' (filterEmitter)="onFilterSelection($event)" [filterData]="filterData"></search-with-filters>
            </div>
        </div>
        <div class="col-12 col-md-6 tablet-full-width m-ml-2 mb-3 px-0 d-flex justify-content-sm-center justify-content-md-end">
            <div class="d-flex w-sm-100 tablet-ml justify-content-sm-center justify-content-md-end align-items-center">
                <span type="button" [ngClass]="{'material-icon-disabled': isListView}" class="material-symbols-outlined mr-2 icon-btn" (click)="setViewType()"> list </span>
                <span type="button" [ngClass]="{'material-icon-disabled': !isListView}" class="material-symbols-outlined mr-2 icon-btn" (click)="setViewType()"> grid_view </span>
                <action-button 
                    [actionList]="actionButtonMetaData.actionList"
                    (selectedActionEmmiter)="onActionSelection($event)"
                    [newFeatureTitle]="'Add Vehicle'"
                    (onOpenAddNew)="addVehiclePopup()">
                </action-button>
            </div>
        </div>
    </div>
</div>

<div class="col-md-12 mb-3 p-0" *ngIf="isListView; else cardView">
    <div class="table-responsive-sm ngx-datatable-custom">
        <ngx-datatable #table class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h"
            [rows]="filteredVehicleRecords ? filteredVehicleRecords : []"
            [columns]="[
                {name:'ID', prop:'vehicle_id', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'py-1 table-cell-text'},
                {name:'Type', prop:'type_of_vehicle', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text',cellTemplate: type},
                {name:'Owner(s)', prop:'tagged_owner', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: taggedOwnerCell},
                {name:'Reg./Serial No.', prop:'serial_number', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text'},
                {name:'Expired Certs', prop:'examination_cert_expiry_date', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: expiredCerts},
                {name:'Last Inspected', prop:'latest_inspection', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: latestInspected},
                {name:'Open Faults', prop:'fault_count', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text'},
                {name:'Status', prop:'', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: listStatus},
                {name:'Action', prop:'', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: listAction}
            ]"
            [columnMode]="'force'"
            [footerHeight]="36"
           [rowHeight]="'auto'"
           [externalPaging]="true"
           [externalSorting]="true"
           [count]="page.totalElements"
           [offset]="page.pageNumber"
           [limit]="page.size"
           (page)="pageCallback($event, false, true)"
           [sortType]="'single'"
           (sort)="sortCallback($event, false)"
           [scrollbarV]="true"
           [virtualization]="false"
           [loadingIndicator]="loadingInlineAssetVehicles">
            <ng-template #type let-row="row" let-column="column">
                <a (click)="viewVehicleOptionModal(row, 1)" class="text-info" href="javascript:void(0)">{{row.type_of_vehicle_name}}</a>
            </ng-template>
            <ng-template #expiredCerts let-value="value" let-row="row" let-column="column">
                {{ row.expired_certs}}
                <i *ngIf="row?.expiry_dates.length"
                    class="fa fa-info-circle"
                    [ngbTooltip]="htmlContent"
                    container="body"
                    aria-hidden="true"></i>
                <ng-template #htmlContent>
                    <ng-container *ngFor="let data of row?.expiry_dates; let i=index">
                        <ng-container *ngIf="data.isExpired === 0; else item_name">
                            <div class="text-center text-white w-100"
                                [ngClass]="data?.isExpired === 0 ? 'background-red' : data?.isExpired === 1 ? 'background-orange' : 'background-green'">
                                <small>{{ data.name | uppercase }} EXPIRED!</small>
                            </div>
                        </ng-container>
                        <ng-template #item_name>
                            <div class="text-center text-white w-100"
                                [ngClass]="data?.isExpired === 0 ? 'background-red' : data?.isExpired === 1 ? 'background-orange' : 'background-green'">
                                <small>{{ data.name }} Expiry: {{ dayjsFormat(data.expiry_date, false) }}</small>
                            </div>
                        </ng-template>
                        <hr *ngIf="i < row?.expiry_dates.length - 1" class="white-line">
                    </ng-container>
                </ng-template>
            </ng-template>
            <ng-template #taggedOwnerCell  let-row="row" let-column="column">
                <div class="text-left">
                    <small>{{ getTaggedOwners(row.tagged_owner) }}</small>
                </div>
            </ng-template>
            <ng-template #latestInspected  let-row="row" let-column="column">
                <div class="text-left">
                    {{row.last_inspected}}
                </div>
            </ng-template>
            <ng-template #listStatus  let-row="row" let-column="column">
                <div class="d-flex">
                    <div *ngIf="row.approval_pending === 1; else noAction">
                        <div *ngIf="(!hasOnlySiteManagement)" class="d-inline-block" ngbDropdown #actionDrop="ngbDropdown" container="body">
                            <button class="btn btn-sm font-md-small btn-outline-primary" ngbDropdownAnchor (click)="actionDrop.open()"> Pending </button>
                            <div ngbDropdownMenu>
                                <button class="dropdown-item cursor-pointer" (click)="approveVehicle(row)"> Approve </button>
                                <button class="dropdown-item btn-delete" (click)="declineVehicleConfirmation(row)"> Decline </button>
                            </div>
                        </div>
                    </div>
                    <ng-template #noAction>
                        <span class="d-flex align-items-center" [ngClass]="{'approved-text': row.approval_pending === 2, 'redText': row.approval_pending === 3}">
                            {{ row.approval_pending_message }}
                        </span>
                    </ng-template>
                </div>
            </ng-template>
            <ng-template #listAction let-row="row" let-column="column">
                <div class="d-flex">
                    <button class="btn btn-sm btn-outline-primary mr-2 d-flex align-items-center" (click)="viewVehicleOptionModal(row, 1)">
                        <span class="material-symbols-outlined font-md-small x-large-font fw-600">search</span>
                    </button>
                </div>
            </ng-template>
        </ngx-datatable>
    </div>
</div>

<ng-template #cardView>
<div class="m-0 p-0 card-deck col-sm-12">
    <ngx-skeleton-loader *ngIf="loadingVehicleRecords || loadingMetaConfig" [count]="(filteredVehicleRecords.length) || 6" [theme]="{ 'border-radius': '4px', height: '214px', width: '258px', 'margin-left': '20px' }"></ngx-skeleton-loader>
    <ng-template ngFor let-item [ngForOf]="filteredVehicleRecords" let-i="index" *ngIf="!loadingVehicleRecords">
        <div class="card mb-3">
            <div *ngIf="item.fault_count" class="custom-badge">
               <div class="badge-pill background-red d-flex align-items-center justify-content-center" style="height: 30px;">
                    <button class="text-white btn btn-sm faultBadgeBtn" (click)="viewVehicleOptionModal(item, 3)">
                        <span class="mr-1"> {{item.fault_count}} Open Faults </span>
                        <i class="fa fa-search" aria-hidden="true"></i>
                    </button>
                </div>
            </div>

            <img *ngIf="!item.vehicle_photos.length" class="card-img-top project-img mt-sm-2 mb-sm-2" style="padding-top: 5px; padding-bottom: 5px;"
                 [src]="'/images/'+item.type_of_vehicle+'.png'"
                 alt="Vehicle Photo"
            />

            <photo-collage
                *ngIf="item.vehicle_photos.length && item.vehicle_photos[0].file_url"
                [ngStyle]="{'margin': (item.approval_pending === 1) ? '0px' : '25px 0px 14px 0px'}"
                [photos]="item.vehicle_photos"
                [uploadBtnSrc]="'/images/equipment-circle.png'"
                [uploadCategory]="'vehicle-photo'"
                [showDeleteFileBtn]="false"
                [collageWidth]="'100px'"
                [collageHeight]="'100px'"
                [addMorePhotos]="false"
                class="mt-1 mb-3"
            >
            </photo-collage>

            <div [ngClass]="{'card-body pt-0 pb-1 text-center': true}">
                <h6 class="card-title" id="rr">
                    <ng-container>
                        <a (click)="viewVehicleOptionModal(item)" class="text-info" href="javascript:void(0)">
                            {{item.vehicle_id != null ? item.vehicle_id + ' - ' + item.type_of_vehicle_name : item.type_of_vehicle_name || ''}}
                        </a>
                    </ng-container>
                </h6>
                <h6 *ngIf="!hasOnlySiteManagement" class="card-subtitle mb-2 text-muted small">Owner(s): {{ getTaggedOwners(item.tagged_owner) }}</h6>
                <p class="card-subtitle mb-2 text-muted small"><span i18n="@@rsn">Reg./Serial Number</span>: {{ item.serial_number }}</p>
                <p *ngIf="item.approval_pending === 2" class="card-subtitle mb-2 text-muted small">
                    Last Daily Inspection: {{ item?.lastDailyInspectionAt ? dayjsFormat(item?.lastDailyInspectionAt?.updatedAt, false) : 'N/A' }}
                </p>
            </div>
            <div *ngIf="item.approval_pending === 1" class="text-center m-1">
                <div *ngIf="hasOnlySiteManagement" class="w-100">
                    <div [ngClass]="{ 'border-bottom-none' : dropdownIndex === i }" class="d-inline-block w-100 background-orange border-redias-5 p-0">
                        <button (click)="openCloseDropdown(i)" class="btn btn-sm font-size-small text-white shadow-none w-100"> Approval Pending
                        </button>
                    </div>
                </div>
                <div *ngIf="!hasOnlySiteManagement" class="w-100">
                    <div [ngClass]="{ 'border-bottom-none' : dropdownIndex === i }" class="d-inline-block w-100 background-black border-redias-5 p-0">
                        <button class="btn btn-sm font-size-small text-white shadow-none w-100" (click)="openCloseDropdown(i)"> Approval Pending
                            <i class="fa fa-angle-down ml-3" [ngClass]="dropdownIndex === i ? 'fa-angle-up' : 'fa-angle-down'" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="dropdown-mask" *ngIf="dropdownIndex === i" (click)="openCloseDropdown(i)"></div>
                    <div class="w-100 dropdownMenu" style="padding-right: 8px; z-index: 9999;">
                        <div class="w-100 bg-white p-0 border-color-black rounded-bottom" *ngIf="dropdownIndex === i">
                            <div class="text-left w-100 pl-2" role="button" (click)="approveVehicle(item)"> <small>Approve</small> </div>
                            <hr class="h-line">
                            <div class="text-left w-100 rounded-bottom pl-2" role="button" (click)="declineVehicleConfirmation(item)"> <small>Decline</small> </div>
                        </div>
                    </div>
                </div>
            </div>
            <div *ngIf="item.approval_pending === 2 && item?.expiry_dates.length > 0" class="text-center m-1">
                <div class="w-100">
                    <div class="d-inline-block w-100 border-redias-5 p-0"
                        [class.border-bottom-none]="dropdownIndex === i && item?.expiry_dates.length > 1"
                        [ngClass]="item?.expiry_dates[0].isExpired === 0 ? 'background-red' : item?.expiry_dates[0].isExpired === 1 ? 'background-orange' : 'background-green'">
                        <button class="btn btn-sm font-size-small text-white shadow-none w-100" (click)="openCloseDropdown(i)">
                            <ng-container *ngIf="item?.expiry_dates[0].isExpired === 0; else item_name">
                                <span >{{ item?.expiry_dates[0].name | uppercase }} EXPIRED!</span>
                            </ng-container>
                            <ng-template #item_name>
                                <span>{{ item?.expiry_dates[0].name }} Expiry: {{ dayjsFormat(item?.expiry_dates[0].expiry_date, false) }}</span>
                            </ng-template>
                            <i *ngIf="item?.expiry_dates.length > 1" class="fa fa-angle-down ml-2" [ngClass]="dropdownIndex === i ? 'fa-angle-up' : 'fa-angle-down'" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div class="dropdown-mask" *ngIf="dropdownIndex === i" (click)="openCloseDropdown(i)"></div>
                    <ng-container *ngIf="item?.expiry_dates.length > 1">
                        <div class="w-100 dropdownMenu" style="padding-right: 8px; z-index: 9999;" (click)="openCloseDropdown(i)">
                            <div class="w-100 bg-white p-0" *ngIf="dropdownIndex === i">
                                <ng-container *ngFor="let data of item?.expiry_dates; let i=index">
                                    <ng-container *ngIf="data.isExpired === 0; else item_name">
                                        <div [class.border-bottom-5]="i === item?.expiry_dates.length - 1" class="text-center text-white w-100" role="button" *ngIf="i !== 0" [ngClass]="data?.isExpired === 0 ? 'background-red' : data?.isExpired === 1 ? 'background-orange' : 'background-green'">
                                            <small>{{ data.name | uppercase }} EXPIRED!</small>
                                        </div>
                                    </ng-container>
                                    <ng-template #item_name>
                                        <div [class.border-bottom-5]="i === item?.expiry_dates.length - 1" class="text-center text-white w-100" role="button" *ngIf="i !== 0" [ngClass]="data?.isExpired === 0 ? 'background-red' : data?.isExpired === 1 ? 'background-orange' : 'background-green'">
                                            <small>{{ data.name }} Expiry: {{ dayjsFormat(data.expiry_date, false) }}</small>
                                        </div>
                                    </ng-template>
                                    <hr *ngIf="i < item?.expiry_dates.length - 1" class="white-line">
                                </ng-container>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
        </div>
    </ng-template>
</div>
<div class="col-12 pt-4 d-flex justify-content-end">
    <ngb-pagination  (pageChange)="paginationCallback($event)"
                     [(page)]="page.pageNumber"
                     [pageSize]="page.size"
                     [collectionSize]="page.totalElements"
                     [boundaryLinks]="page.totalElements > page.size"
                     [maxSize]="5"
                     [rotate]="true"
    >
    </ngb-pagination>
</div>
<div class="col-sm-12" *ngIf="!vehiclesLoading && !filteredVehicleRecords.length && !loadingVehicleRecords">
    <p class="col-sm text-center" > No vehicles found.</p>
</div>
</ng-template>


<block-loader [show]="(vehiclesLoading)" alwaysInCenter="true" zIndex="1200" showBackdrop="true"></block-loader>

<i-modal #addVehicleModalRef [title]="(!assetVehicle?.id) ? 'Add Vehicle' : 'Vehicle Information'" size="lg" (onCancel)="closeVehicleModal()" cancelBtnText="Close" (onClickRightPB)="saveVehicle()" 
    [rightPrimaryBtnTxt]="(!assetVehicle?.id) ? 'Add' : 'Update'" [rightPrimaryBtnDisabled]="!addVehicleForm.valid">
        <form novalidate #addVehicleForm="ngForm" class="form-container">
            <div class="form-group row" *ngIf="!assetTypeSelected">
                <div class="col-sm-12 p-0">
                    <div class="col-sm-3 p-0 pr-1 float-right">
                        <photo-collage
                            *ngIf="showPhotoCollage"
                            [photos]="assetVehicle.vehicle_photos"
                            [uploadBtnSrc]="'/images/equipment-circle.png'"
                            [uploadCategory]="'vehicle-photo'"
                            (photoUploadDone)="vehiclePhotoCollageUploadDone($event, true)"
                            (photoDeleteDone)="fileDeleteDone($event)"
                        >
                        </photo-collage>
                    </div>
                </div>

                <div class="col-sm-12 p-0">
                    <div class="col-sm-3 mt-2 p-0 float-right" style="position: relative; left: 20px;">
                        <file-uploader-v2
                            [disabled]="false"
                            [chooseFileBtnText]="'+ Add Photos'"
                            [showDragnDrop]="false"
                            [category]="'vehicle-photo'"
                            (uploadDone)="vehiclePhotoUploadDone($event)"
                            [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png']"
                            [showFileName]="false"
                            [showThumbnail]="true"
                            [showViewFileModal]="false"
                            [hide_output]="true"
                        >
                        </file-uploader-v2>
                    </div>
                </div>
            </div>

            <div class="form-group row">
                <label>Type of vehicle: <small class="required-asterisk ">*</small></label>
                <ng-select [(ngModel)]="assetVehicle.type_of_vehicle" [items]="TYPE_OF_VEHICLE" bindLabel="alternate_phrase" bindValue="key" name="type_of_vehicle" class="w-100 dropdown-list" appendTo="body" required ng-value="assetVehicle.type_of_vehicle" (change)="assetTypeChanged()">
                </ng-select>
            </div>
            <fieldset *ngIf="assetVehicle.type_of_vehicle">
                <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.vehicle_id?.alwaysDisplay || assetConfig.defaultFields?.vehicle_id?.display)">
                    <label>
                        Vehicle ID: <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control" #vehicle_id="ngModel" [(ngModel)]="assetVehicle.vehicle_id" name="vehicle_id"
                           placeholder="Vehicle ID" [required]="assetConfig.defaultFields?.vehicle_id?.required" [maxlength]="maxlength"/>
                    <span class="small text-danger mx-1">
                        Characters remaining: {{(maxlength - (assetVehicle.vehicle_id || '').length)}}/{{maxlength}}
                    </span>
                    <div class="alert alert-danger" [hidden]="(vehicle_id.valid)">Vehicle Id is required.</div>
                </div>
    
                <div *ngIf="assetVehicle.type_of_vehicle && assetVehicle.type_of_vehicle != 'van'">
                    <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.power_output?.alwaysDisplay || assetConfig.defaultFields?.power_output?.display)">
                        <label>
                            Power Output:
                        </label>
                        <div class="col-md-12 p-0 d-inline-block">
                            <input type="number" class="form-control col-md-11 d-inline-block" [(ngModel)]="assetVehicle.power_output" name="power_output"
                               placeholder="Power Output" min="0" [required]="assetConfig.defaultFields?.power_output?.required"/>
                            <strong class="col-md-1 p-0 ml-3" style="font-family: 'Font Awesome 5 Free';">kW</strong>
                        </div>
                    </div>
    
                    <div class="form-group row" *ngIf="assetVehicle.power_output && (assetVehicle.power_output >= 37 && assetVehicle.power_output <= 560)">
                        <label>
                            Type Approval Number (TAN) (if applicable):
                        </label>
                        <input type="text" class="form-control" [(ngModel)]="assetVehicle.approval_number" name="approval_number"
                               placeholder="TAN" [maxlength]="maxlength"/>
                        <span class="small text-danger mx-1">
                            Characters remaining: {{(maxlength - (assetVehicle.approval_number || '').length)}}/{{maxlength}}
                        </span>
                    </div>
                    
                    <div class="form-group row"  *ngIf="assetTypeSelected || (assetConfig.defaultFields?.engine_manufacturer?.alwaysDisplay || assetConfig.defaultFields?.engine_manufacturer?.display)">
                        <label>
                            Engine Manufacturer: <small class="required-asterisk" *ngIf="assetConfig.defaultFields?.engine_manufacturer?.required">*</small>
                        </label>
                        <input type="text" class="form-control" [(ngModel)]="assetVehicle.engine_manufacturer" name="engine_manufacturer"
                               placeholder="Engine Manufacturer" [required]="assetConfig.defaultFields?.engine_manufacturer?.required" [maxlength]="maxlength"/>
                        <span class="small text-danger mx-1">
                            Characters remaining: {{(maxlength - (assetVehicle.engine_manufacturer || '').length)}}/{{maxlength}}
                        </span>
                    </div>
    
                    <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.year_manufactured?.alwaysDisplay || assetConfig.defaultFields?.year_manufactured?.display)">
                        <label>
                            Year Manufactured: <small class="required-asterisk" *ngIf="assetConfig.defaultFields?.year_manufactured?.required">*</small>
                        </label>
                        <ng-select [(ngModel)]="assetVehicle.year_manufactured" [items]="years" name="year_manufactured" class="w-100" ng-value="assetVehicle.year_manufactured" [required]="assetConfig.defaultFields?.year_manufactured?.required">
                        </ng-select>
                    </div>
                </div>
    
                <div class="form-group row" *ngIf="assetTypeSelected || ((!hasOnlySiteManagement && showVehicleModal) &&  (assetConfig.defaultFields?.tagged_owner?.alwaysDisplay || assetConfig.defaultFields?.tagged_owner?.display))">
                    <label>Owner(s): <small class="required-asterisk ">*</small></label>
                    <company-selector-v2
                        [required]="true"
                        [country_code]="project?.custom_field?.country_code"
                        name="tagged_owner"
                        [selectId]="assetVehicle.tagged_owner"
                        placeholder="Select Owner(s)"
                        class="w-100 dropdown-list"
                        [disabled]="hasOnlySiteManagement"
                        [multiple]="true"
                        (selectionChanged)="assetVehicle.tagged_owner = $event.selected"
                        [projectId]="projectId"
                    ></company-selector-v2>
                </div>
    
                <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.serial_number?.alwaysDisplay || assetConfig.defaultFields?.serial_number?.display)">
                    <label>
                        Reg./Serial Number: <small class="required-asterisk " *ngIf="assetConfig.defaultFields?.serial_number?.required">*</small>
                    </label>
                    <input type="text" class="form-control" required #serial_number="ngModel" [(ngModel)]="assetVehicle.serial_number" name="serial_number"
                           placeholder="Reg./Serial Number" [required]="assetConfig.defaultFields?.serial_number?.required" [maxlength]="maxlength"/>
                    <span class="small text-danger mx-1">
                        Characters remaining: {{(maxlength - (assetVehicle.serial_number || '').length)}}/{{maxlength}}
                    </span>
                    <div class="alert alert-danger" [hidden]="(serial_number.valid)">Reg./Serial Number is required.</div>
                </div>
    
                <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.arrived_at?.alwaysDisplay || assetConfig.defaultFields?.arrived_at?.display)">
                    <label class="col-sm-12 p-0">
                        Arrived on site: <small class="required-asterisk " *ngIf="assetConfig.defaultFields?.arrived_at?.required">*</small>
                    </label>
                    <div class="input-group col-sm-8 p-0">
                        <input #aa="ngbDatepicker" [(ngModel)]="assetVehicle._arrivedAt"
                               class="form-control" name="arrived_at" ng-value="assetVehicle._arrivedAt"
                               ngbDatepicker [placeholder]="displayDateFormat" [required]="assetConfig.defaultFields?.arrived_at?.required"
                               readonly>
                        <div class="input-group-append">
                            <button (click)="aa.toggle()" class="btn btn-outline-secondary calendar" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div *ngIf="assetTypeSelected || (assetConfig.defaultFields?.examination_certification?.alwaysDisplay || assetConfig.defaultFields?.examination_certification?.display)" >
                    <div class="form-group row">
                        <label>
                            Thorough Examination Certificate Number: <small class="required-asterisk " *ngIf="assetConfig.defaultFields?.examination_certification?.required && assetConfig.defaultFields?.examination_certification?.document_number_mandatory">*</small>
                        </label>
                        <input type="text" class="form-control" #examination_cert_number="ngModel" [(ngModel)]="assetVehicle.examination_cert_number" name="examination_cert_number"
                               (change)="examinationNumberChanged()" placeholder="Thorough examination certificate number" [required]="assetConfig.defaultFields?.examination_certification?.required && assetConfig.defaultFields?.examination_certification?.document_number_mandatory" [maxlength]="maxlength"/>
                        <span class="small text-danger mx-1">
                            Characters remaining: {{(maxlength - (assetVehicle.examination_cert_number || '').length)}}/{{maxlength}}
                        </span>
                    </div>
        
                    <div class="form-group row" *ngIf="hasExaminationNumber()">
                        <label class="col-sm-12 p-0">
                            Thorough Examination Certificate Expiry Date: <small class="required-asterisk ">*</small>
                        </label>
                        <div class="input-group col-sm-8 p-0">
                            <input class="form-control" [placeholder]="displayDateFormat" readonly (dateSelect)="examinationCertExpDateChanged()"
                                   name="examination_cert_expiry_date" [(ngModel)]="assetVehicle._examinationCertExpiryDate" ngbDatepicker
                                   #eced="ngbDatepicker" ng-value="assetVehicle._examinationCertExpiryDate" [minDate]="minDate" [required]="assetConfig.defaultFields?.examination_certification?.expiry_date_mandatory">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary calendar" (click)="eced.toggle()" type="button">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </div>
                        </div>
                    </div>
        
                    <div class="form-group row" *ngIf="hasExaminationCertExpDate()">
                        <label class="col-md-6 p-0">
                            Thorough Examination Certificate: <small *ngIf="assetConfig.defaultFields?.examination_certification?.attachment_mandatory" class="required-asterisk ">*</small>
                        </label>
                        <ng-template ngFor let-item [ngForOf]="(assetVehicle.examination_certificates || [])" let-i="index">
                            <div class="col-sm-10 p-0 pl-3">
                                <file-uploader-v2
                                    [disabled]="false"
                                    [category]="'examination-certificate'"
                                    [init]="item"
                                    (uploadDone)="examinationCertificateUploadDone($event)"
                                    [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                                    [showViewFileModal]="item && item.file_url"
                                    [fileModalTitle]="'Examination Certificate'"
                                    [showFileName]="false"
                                    (deleteFileDone)="deleteExaminationCertificateRecord($event)"
                                    [showDeleteBtn]="true"
                                    [showThumbnail]="false"
                                    [chooseFileBtnText]="'+ Add Certificate'">
                                </file-uploader-v2>
                                <div class="alert alert-danger" *ngIf="assetVehicle?._examinationCertExpiryDate && assetConfig.defaultFields?.examination_certification?.attachment_mandatory && assetVehicle.examination_certificates.length <= 1">
                                    <input type="hidden" id="certification-attachment-exam" name="certification-attachment-exam" [ngModel]="assetVehicle.examination_certificates.length >=2 ? true: undefined"required>
                                    Thorough Examination Certificate is required.
                                </div>
                                
                            </div>
                        </ng-template>
                    </div>
                </div>
                
    
                <div class="form-group row" *ngIf="assetTypeSelected || (assetConfig.defaultFields?.service_certification?.alwaysDisplay || assetConfig.defaultFields?.service_certification?.display)" >
                    <label class="col-sm-12 p-0">
                        Service Expiry Date: <small class="required-asterisk" *ngIf="assetConfig.defaultFields?.service_certification?.required && assetConfig.defaultFields?.service_certification?.expiry_date_mandatory">*</small>
                    </label>
                    <div class="input-group col-sm-8 p-0">
                        <input class="form-control" [placeholder]="displayDateFormat" readonly
                               name="service_expiry_date" [(ngModel)]="assetVehicle._serviceExpiryDate" ngbDatepicker
                               #sed="ngbDatepicker" ng-value="assetVehicle._serviceExpiryDate"
                               [minDate]="minDate" [required]="assetConfig.defaultFields?.service_certification?.required && assetConfig.defaultFields?.service_certification?.expiry_date_mandatory">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary calendar" (click)="sed.toggle()" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>
    
                <div class="form-group row" *ngIf="hasServiceExpDate()">
                    <label class="col-md-5 p-0">
                        Service Certificate: <small *ngIf="assetConfig.defaultFields?.service_certification?.attachment_mandatory" class="required-asterisk ">*</small>
                    </label>
                    <ng-template ngFor let-item [ngForOf]="(assetVehicle.service_certificates || [])" let-i="index">
                        <div class="col-sm-10 p-0 pl-3">
                            <file-uploader-v2
                                [disabled]="false"
                                [category]="'service-certificate'"
                                [init]="item"
                                (uploadDone)="serviceCertificateUploadDone($event)"
                                [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                                [showViewFileModal]="item && item.file_url"
                                [fileModalTitle]="'Service Certificate'"
                                [showFileName]="false"
                                (deleteFileDone)="deleteServiceCertificateRecord($event)"
                                [showDeleteBtn]="true"
                                [showThumbnail]="false"
                                [chooseFileBtnText]="'+ Add Certificate'">
                            </file-uploader-v2>
                            <div class="alert alert-danger" *ngIf="assetVehicle?._serviceExpiryDate && assetConfig.defaultFields?.service_certification?.attachment_mandatory && assetVehicle.service_certificates.length <= 1">
                                <input type="hidden" id="certification-attachment-exam" name="certification-attachment-exam" [ngModel]="assetVehicle.service_certificates.length >=2 ? true: undefined" required>
                                Service Certificate is required.
                            </div>
                        </div>
                    </ng-template>
                </div>
    
                <div class="form-group row" *ngIf="assetVehicle.type_of_vehicle === 'van' && assetConfig.defaultFields?.mot_certification?.display" >
                    <label class="col-sm-12 p-0">
                        MOT Expiry Date: <small *ngIf="assetConfig.defaultFields?.mot_certification?.required && assetConfig.defaultFields?.mot_certification?.expiry_date_mandatory " class="required-asterisk ">*</small>
                    </label>
                    <div class="input-group col-sm-8 p-0">
                        <input class="form-control" [placeholder]="displayDateFormat" readonly
                               name="mot_expiry_date" [(ngModel)]="assetVehicle._motExpiryDate" ngbDatepicker
                               #med="ngbDatepicker" ng-value="assetVehicle._motExpiryDate"
                               [minDate]="minDate" [required]="assetConfig.defaultFields?.mot_certification?.required && assetConfig.defaultFields?.mot_certification?.expiry_date_mandatory">
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary calendar" (click)="med.toggle()" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>
    
                <div class="form-group row" *ngIf="assetVehicle.type_of_vehicle === 'van' && hasMOTExpDate()">
                    <label class="col-md-5 p-0">
                        MOT Certificate: <small *ngIf="assetConfig.defaultFields?.mot_certification?.attachment_mandatory" class="required-asterisk ">*</small>
                    </label>
                    <ng-template ngFor let-item [ngForOf]="(assetVehicle.mot_certificates || [])" let-i="index">
                        <div class="col-sm-10 p-0 pl-3">
                            <file-uploader-v2
                                [disabled]="false"
                                [category]="'mot-certificate'"
                                [init]="item"
                                (uploadDone)="motCertificateUploadDone($event)"
                                [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                                [showViewFileModal]="item && item.file_url"
                                [fileModalTitle]="'MOT Certificate'"
                                [showFileName]="false"
                                (deleteFileDone)="deleteMOTCertificateRecord($event)"
                                [showDeleteBtn]="true"
                                [showThumbnail]="false"
                                [chooseFileBtnText]="'+ Add Certificate'">
                            </file-uploader-v2>
                            <div class="alert alert-danger" *ngIf="assetVehicle?._motExpiryDate && assetConfig.defaultFields?.mot_certification?.attachment_mandatory && assetVehicle.mot_certificates.length <= 1">
                                <input type="hidden" id="certification-attachment-exam" name="certification-attachment-exam" [ngModel]="assetVehicle.mot_certificates.length >=2 ? true: undefined" required>
                                MOT Certificate is required.
                            </div>
                        </div>
                    </ng-template>
                </div>
            </fieldset>
            
            
            <ng-container *ngFor="let field of assetConfig.custom_fields;let i = index; trackBy: trackByKey">
                <div class="form-group row" *ngIf="field.type === assetCustomConfigFieldTypes.Text">
                    <label> {{field.title}} <small class="required-asterisk" *ngIf="field.required">*</small>
                    </label>
                    <input type="text" class="form-control" #item="ngModel" [(ngModel)]="assetVehicle.custom_fields[i].value" [name]="field.title" placeholder="Enter text" [required]="field.required"  [maxlength]="maxlength"/>
                    <span class="small text-danger mx-1">
                        Characters remaining: {{(maxlength - (assetVehicle.custom_fields[i].value || '').length)}}/{{maxlength}}
                    </span>
                    <!-- <div class="alert alert-danger" [hidden]="(item.valid)">Item is required.</div> -->
                </div>
                <div class="form-group row" *ngIf="field.type === assetCustomConfigFieldTypes.Dropdown">
                    <label> {{field.title}}: <small class="required-asterisk" *ngIf="field.required">*</small>
                    </label>
                    <ng-select class="w-100 dropdown-list" appendTo="body" [(ngModel)]="assetVehicle.custom_fields[i].value" placeholder="Select option" [items]="field.options" [name]="field.title" [required]="field.required">
                    </ng-select>
                </div>
                <div class="form-group row" *ngIf="field.type === assetCustomConfigFieldTypes.Date">
                    <label class="col-sm-12 p-0">
                        {{field.title}}: <small class="required-asterisk" *ngIf="field.required">*</small>
                    </label>
                    <div class="input-group col-sm-8 p-0">
                        <input #aa="ngbDatepicker" [(ngModel)]="assetVehicle.custom_fields[i].value" class="form-control" [name]="field.title" ngbDatepicker [placeholder]="displayDateFormat" [required]="field.required" readonly>
                        <div class="input-group-append">
                            <button (click)="aa.toggle()" class="btn btn-outline-secondary calendar" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <ng-container *ngIf="field.type === assetCustomConfigFieldTypes.Certification">

                    <div class="form-group row">
                        <label class="col-sm-12 p-0">
                            {{field.title}} Expiry Date: <small class="required-asterisk" *ngIf="field.required">*</small>
                        </label>
                        <div class="input-group col-sm-8 p-0">
                            <input class="form-control" [placeholder]="displayDateFormat" readonly
                                   name="expiry_date_{{i}}" [(ngModel)]="assetVehicle.custom_fields[i]._examinationCertExpiryDate" ngbDatepicker
                                   #eced="ngbDatepicker" ng-value="assetEquipment._examinationCertExpiryDate"
                                   [minDate]="minDate"
                                   [required]="field.required">
                            <div class="input-group-append">
                                <button class="btn btn-outline-secondary calendar" (click)="eced.toggle()" type="button">
                                    <i class="fa fa-calendar"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row" *ngIf="assetVehicle.custom_fields[i]._examinationCertExpiryDate">
                        <label>
                            {{field.title}} Certificate Number: <small class="required-asterisk" *ngIf="field.required && field.document_number_mandatory">*</small>
                        </label>
                        <input type="text" class="form-control" [(ngModel)]="assetVehicle.custom_fields[i].certificate_number" [name]="field.title+'_number'"
                               placeholder="Enter certificate number" [required]="field.required && field.document_number_mandatory" [maxlength]="maxlength"/>
                        <span class="small text-danger mx-1">
                            Characters remaining: {{(maxlength - (assetVehicle.custom_fields[i].certificate_number || '').length)}}/{{maxlength}}
                        </span>
                    </div>

                    <div class="form-group row" *ngIf="assetVehicle.custom_fields[i]._examinationCertExpiryDate">
                        <label class="col-md-7 p-0">
                            {{field.title}} Attachment: <small *ngIf="field.attachment_mandatory" class="required-asterisk ">*</small>
                        </label>
                        <ng-template ngFor let-item [ngForOf]="(assetVehicle.custom_fields[i].certificates || [])" let-j="index">
                            <div class="col-sm-10 p-0 pl-3">
                                <file-uploader-v2
                                    [disabled]="false"
                                    [category]="'custom-certificate'"
                                    [init]="item"
                                    (uploadDone)="certificateUploadDone($event, i)"
                                    [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                                    [showViewFileModal]="item && item.file_url"
                                    [fileModalTitle]="field.title + ' Certificate'"
                                    [showFileName]="false"
                                    (deleteFileDone)="deleteCertificateRecord($event, i)"
                                    [showDeleteBtn]="true"
                                    [showThumbnail]="false"
                                    [chooseFileBtnText]="'+ Add Certificate'"
                                >
                                </file-uploader-v2>
                                <div class="alert alert-danger" *ngIf="field.required && field.attachment_mandatory && assetVehicle.custom_fields[i].certificates.length <= 1">
                                    <input type="text" class="d-none" name="certification-attachment-{{i}}" [ngModel]="assetVehicle.custom_fields[i].certificates.length >=2 ? true: undefined" required>
                                    {{field.title}} attachment is required.
                                </div>
                            </div>
                        </ng-template>
                    </div>
        
                </ng-container>
                

            </ng-container>
            
        </form>
</i-modal>

<i-modal #viewFileRef [title]="fileModalTitle" size="lg" cancelBtnText="Close" (onCancel)="closePdfImgModal()" (onClickRightSB)="closePdfImgModal($event)">
    <div class="form-group row" *ngIf="showPdfImg">
        <div class="col-sm-12 mt-2 text-center">
            <div *ngIf="!isPdfDocument" style="width: 600px; display:inline-block;" class="d-inline-block">
                <img [src]="certificateUrl" style="width: 100%; height: auto;" #img />
            </div>

            <iframe *ngIf="certificateUrl && isPdfDocument" class="border-0" [src]="certificatePreviewURL" width="750px" height="500px">
            </iframe>
        </div>
    </div>
</i-modal>

<i-modal #viewVehicleOptionsRef [title]="(assetVehicle?.vehicle_id) ? assetVehicle?.vehicle_id + ' - ' + getVehicleType(assetVehicle?.type_of_vehicle) + '(' + assetVehicle?.serial_number + ')' : ''" 
    size="lg" [showCancel]="false" [rightPrimaryBtnTxt]="(assetVehicle.id && !assetVehicle.is_archived) ? 'Archive' : 'Unarchive'" [rightSecondaryBtnTxt]="(activeNav === 1) ? 'Edit' : ''"
         (onClickRightPB)="(assetVehicle.id && !assetVehicle.is_archived) ? archiveVehicleAsset(assetVehicle.id) : unArchiveVehicleAsset(assetVehicle.id, false, true)"
    (onClickRightSB)="getVehicle(assetVehicle?.id, true, false)">
        <div class="card-body">
            <div class="row">
                <ul ngbNav #nav="ngbNav" [(activeId)]="activeNav" class="nav-tabs n-tab">
                    <li [ngbNavItem]="1">
                        <a ngbNavLink class="nav-a" (click)="toggler(1)">Details</a>
                        <ng-template ngbNavContent>
                            <div class="">
                                <div class="col-sm-12 p-0">
                                    <div class="w-100" style="height: 10rem" *ngIf="assetVehicle.vehicle_photos?.length && assetVehicle.vehicle_photos[0].file_url">
                                        <div style="position: absolute; right: 0px;">
                                            <photo-collage
                                            [photos]="assetVehicle.vehicle_photos"
                                            [uploadBtnSrc]="'/images/equipment-circle.png'"
                                            [uploadCategory]="'vehicle-photo'"
                                            (photoUploadDone)="vehiclePhotoUploadDone($event, true)"
                                            (photoDeleteDone)="fileDeleteDone($event)"
                                            [showDeleteFileBtn]="false"
                                            [addMorePhotos]="false"
                                            >
                                            </photo-collage>
                                        </div>
                                    </div>
                                </div>
                                <table class="table table-sm table-bordered">
                                    <tbody>
                                    <tr *ngIf="assetVehicle?.vehicle_id">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Vehicle ID:</strong> </td>
                                        <td colspan="3"> {{ assetVehicle.vehicle_id }} </td>
                                    </tr>
                                    <tr *ngIf="assetVehicle?.type_of_vehicle">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Type of vehicle:</strong> </td>
                                        <td colspan="3"> {{ getVehicleType(assetVehicle.type_of_vehicle) }} </td>
                                    </tr>
                                    <tr *ngIf="assetVehicle?.power_output">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Power Output:</strong> </td>
                                        <td colspan="3"> {{ assetVehicle.power_output }}kW</td>
                                    </tr>
                                    <tr *ngIf="assetVehicle?.approval_number">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Type Approval Number (TAN) (if applicable):</strong> </td>
                                        <td colspan="3"> {{ assetVehicle.approval_number }} </td>
                                    </tr>
                                    <tr *ngIf="assetVehicle?.engine_manufacturer">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Engine Manufacturer:</strong> </td>
                                        <td colspan="3"> {{ assetVehicle.engine_manufacturer }} </td>
                                    </tr>
                                    <tr *ngIf="assetVehicle?.year_manufactured">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Year Manufactured:</strong> </td>
                                        <td colspan="3"> {{ assetVehicle.year_manufactured }} </td>
                                    </tr>
                                    <tr *ngIf="!hasOnlySiteManagement && assetVehicle.tagged_owner.length">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Owner(s):</strong> </td>
                                        <td colspan="3"> {{ getTaggedOwners(assetVehicle.tagged_owner) }} </td>
                                    </tr>
                                    <tr *ngIf="assetVehicle?.serial_number">
                                        <td width="30%" class="tr-bg-dark-color"> <strong><span i18n="@@rsn">Reg./Serial Number</span>:</strong> </td>
                                        <td colspan="3"> {{ assetVehicle.serial_number }} </td>
                                    </tr>
                                    <tr *ngIf="assetVehicle?.arrived_at">
                                        <td class="tr-bg-dark-color" width="30%"> <strong>Arrived on site:</strong> </td>
                                        <td colspan="3"> {{ dayjsFormat(assetVehicle.arrived_at, false) }} </td>
                                    </tr>
                                    <tr *ngIf="assetVehicle.examination_cert_number || assetVehicle.examination_cert_expiry_date || assetVehicle?.examination_certificates?.length - 1">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Thorough Examination Certificate:</strong> </td>
                                        <td width="17.5%" class="p-0">
                                            <div class="w-100 tr-bg-dark-color custom-hight custom-padding">Document No.</div>
                                            <div class="w-100 custom-padding">{{ assetVehicle.examination_cert_number }}</div>
                                        </td>
                                        <td width="17.5%" class="p-0">
                                            <div class="w-100 tr-bg-dark-color custom-hight custom-padding">Expiry Date</div>
                                            <div class="w-100 custom-padding">{{ dayjsFormat(assetVehicle.examination_cert_expiry_date, false) }}</div>
                                        </td>
                                        <td width="35%" class="p-0">
                                            <div class="w-100 tr-bg-dark-color custom-hight custom-padding">Attachments</div>
                                            <div class="w-100 custom-padding">
                                                <p class="upload-name mb-0" *ngFor="let img of assetVehicle?.examination_certificates">
                                                    <a href="javascript:void(0)" (click)="viewFileModal(img.file_url, 'Examination Certificate')">{{ img?.name }}</a>
                                                </p>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr *ngIf="assetVehicle.service_expiry_date || assetVehicle?.service_certificates?.length - 1">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>Service:</strong> </td>
                                        <td width="30%" colspan="2" class="p-0">
                                            <div class="w-100 tr-bg-dark-color custom-padding">Expiry Date</div>
                                            <div class="w-100 custom-padding">{{ dayjsFormat(assetVehicle.service_expiry_date, false) }}</div>
                                        </td>
                                        <td width="30%" class="p-0">
                                            <div class="w-100 tr-bg-dark-color custom-padding">Attachments</div>
                                            <div class="w-100 custom-padding">
                                                <p class="upload-name mb-0" *ngFor="let img of assetVehicle?.service_certificates">
                                                    <a href="javascript:void(0)" (click)="viewFileModal(img.file_url, 'Service Certificate')">{{ img?.name }}</a>
                                                </p>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr *ngIf="assetVehicle.mot_expiry_date || assetVehicle.mot_certificates?.length -1">
                                        <td width="30%" class="tr-bg-dark-color"> <strong>MOT:</strong> </td>
                                        <td width="30%" colspan="2" class="p-0">
                                            <div class="w-100 tr-bg-dark-color custom-padding">Expiry Date</div>
                                            <div class="w-100 custom-padding">{{ dayjsFormat(assetVehicle.mot_expiry_date, false) }}</div>
                                        </td>
                                        <td width="30%" class="p-0">
                                            <div class="w-100 tr-bg-dark-color custom-padding">Attachments</div>
                                            <div class="w-100 custom-padding">
                                                <p class="upload-name mb-0" *ngFor="let img of assetVehicle.mot_certificates">
                                                    <a href="javascript:void(0)" (click)="viewFileModal(img.file_url, 'MOT Certificate')">{{ img?.name }}</a>
                                                </p>
                                            </div>
                                        </td>
                                    </tr>
                                    <ng-container *ngFor="let field of assetVehicle.custom_fields;let i = index; trackBy: trackByKey">
                                        <tr *ngIf="field.type === assetCustomConfigFieldTypes.Text || field.type === assetCustomConfigFieldTypes.Dropdown">
                                            <td class="tr-bg-dark-color" width="30%"> <strong>{{field.title}}:</strong> </td>
                                            <td colspan="3"> {{ field.value }} </td>
                                        </tr>
                                        <tr *ngIf="field.type === assetCustomConfigFieldTypes.Date">
                                            <td class="tr-bg-dark-color" width="30%"> <strong>{{field.title}}:</strong> </td>
                                            <td colspan="3"> {{ dayjsFormat(field.value, false) }} </td>
                                        </tr>
                                        <tr *ngIf="field.type === assetCustomConfigFieldTypes.Certification">
                                            <td width="30%" class="tr-bg-dark-color"> <strong>{{field.title}}:</strong> </td>
                                            <td width="17.5%" class="p-0">
                                                <div class="w-100 tr-bg-dark-color custom-padding">Document No.</div>
                                                <div class="w-100 custom-padding">{{ field.certificate_number }}</div>
                                            </td>
                                            <td width="17.5%" class="p-0">
                                                <div class="w-100 tr-bg-dark-color custom-padding">Expiry Date</div>
                                                <div class="w-100 custom-padding" *ngIf="field.expiry_date">{{ dayjsFormat(field.expiry_date, false) }}</div>
                                            </td>
                                            <td width="35%" class="p-0">
                                                <div class="w-100 tr-bg-dark-color custom-padding">Attachments</div>
                                                <div class="w-100 custom-padding">
                                                    <p class="upload-name mb-0" *ngFor="let cert of field.certificates">
                                                        <a href="javascript:void(0)" (click)="viewFileModal(cert?.file_url, 'Examination Certificate')">{{ cert?.name }}</a>
                                                    </p>
                                                </div>
                                            </td>
                                        </tr>
                                    </ng-container>
                                    </tbody>
                                </table>
                            </div>
                        </ng-template>
                    </li>
                    <li [ngbNavItem]="2">
                        <a ngbNavLink class="nav-a" (click)="toggler(2)">Inspections</a>
                        <ng-template ngbNavContent>
                            <div class="overflow-hidden">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="tr-bg-dark-color vertical-align-middle p-1 px-2 date-col-width"> Week Commencing </th>
                                            <th class="tr-bg-dark-color vertical-align-middle p-1 px-2 td-col-width" *ngFor="let day of week_Days"> {{ day }} </th>
                                            <th class="tr-bg-dark-color vertical-align-middle p-1 px-2"> Actions </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <ng-container *ngIf="hasInspections">
                                            <tr *ngFor="let row of assetVehicle?.availableMondays; let i=index">
                                                <td class="vertical-align-middle p-1 px-2"> {{row?.date}} </td>
                                                <td class="vertical-align-middle p-1 px-2 text-center" *ngFor="let day of week_Days">
                                                    <ng-container *ngIf="dayIndex(row.weekdays, day) !== -1">
                                                        <i class="fa fa-check-circle text-success" aria-hidden="true" [ngbTooltip]="' Daily inspection was carried out by '&nbsp;+row?.weekdays[dayIndex(row.weekdays, day)]?.user_name+' on '&nbsp;+row?.weekdays[dayIndex(row.weekdays, day)]?.createdAt"></i>
                                                    </ng-container>
                                                </td>
                                                <td class="vertical-align-middle p-1 px-2">
                                                    <button class="btn btn-sm btn-outline-primary mr-1 pl-1 pr-1 pt-0 pb-0" (click)="downloadWeeklyInspection(row.date, 'html', assetVehicle?.id)">
                                                        <i class="fa fa-search"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-primary pl-1 pr-1 pt-0 pb-0" (click)="downloadWeeklyInspection(row.date, 'pdf', assetVehicle?.id)">
                                                        <i class="fa fa-download"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                        </ng-container>
                                        <tr *ngIf="!hasInspections">
                                            <td colspan="9" class="text-center"> No inspections found </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </ng-template>
                    </li>

                    <li [ngbNavItem]="3">
                        <a ngbNavLink class="nav-a" (click)="toggler(3)">Faults</a>
                        <ng-template ngbNavContent>
                            <div class="overflow-hidden">
                                <table class="table table-sm table-bordered">
                                <thead>
                                <tr>
                                    <th class="tr-bg-dark-color vertical-align-middle">Fault No.</th>
                                    <th class="tr-bg-dark-color vertical-align-middle">Reported</th>
                                    <th class="tr-bg-dark-color vertical-align-middle">Reported By</th>
                                    <th class="tr-bg-dark-color vertical-align-middle">Fault Details</th>
                                    <th class="tr-bg-dark-color vertical-align-middle">Status</th>
                                    <th class="tr-bg-dark-color vertical-align-middle">Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr *ngIf="!vehicleFaults.length else faultTable">
                                    <td colspan="9" class="text-center"> No faults found</td>
                                </tr>
                                <ng-template #faultTable>
                                    <ng-container *ngFor="let fault of vehicleFaults; let i=index">
                                        <tr [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                            <td [rowSpan]="getRowSpan(fault)">{{fault.fault_id}}</td>
                                            <td>
                                                <span *ngIf="fault.date_reported">{{ dayjsFormat(fault.date_reported) }}</span>
                                            </td>
                                            <td>
                                                <span *ngIf="fault.reported_by">{{ fault.reported_by }}</span>
                                            </td>
                                            <td>
                                                <span *ngIf="fault.fault">{{ fault.fault}}</span>
                                            </td>

                                            <td>
                                                <span *ngIf="fault.status" [ngClass]="{'redText': (fault.status == 'open'), 'greenText': (fault.status == 'closed')}">
                                                    {{ (fault.status || '').toUpperCase() }}
                                                </span>
                                            </td>
                                            <td class="text-center">
                                                <button *ngIf="fault.status && fault.status == 'open'"  title="Close Out" (click)="faultCloseoutModal(fault)" class="btn btn-sm btn-outline-primary">
                                                    <i class="fa fa-times"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <tr *ngIf="fault.images && fault.images.length" [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                            <td colspan="5">
                                                <pop-up-image-viewer [alt]="'Image'" [imgArray]="fault.images || []"></pop-up-image-viewer>
                                            </td>
                                        </tr>
                                        <tr *ngIf="fault.closedout_at" [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                            <td colspan="5" class="p-0">
                                                <table class="table table-bordered mb-0">
                                                    <thead>
                                                        <td class="tr-bg-dark-color vertical-align-middle p-1 px-2">Closed Out</td>
                                                        <td class="tr-bg-dark-color vertical-align-middle p-1 px-2">Closeout By</td>
                                                        <td class="tr-bg-dark-color vertical-align-middle p-1 px-2">Closeout Details</td>
                                                    </thead>
                                                    <tbody>
                                                    <td class="vertical-align-middle p-1 px-2">
                                                        <span *ngIf="fault.closedout_at">{{ dayjsFormat(fault.closedout_at) }}</span>
                                                    </td>
                                                    <td class="vertical-align-middle p-1 px-2">
                                                        <span *ngIf="fault.closedout_by">{{ fault.closedout_by }}</span>
                                                    </td>
                                                    <td class="vertical-align-middle p-1 px-2">
                                                        <span *ngIf="fault.closedout_details">{{ fault.closedout_details }}</span>
                                                    </td>
                                                    </tbody>
                                                </table>
                                            </td>
                                        </tr>
                                        <tr *ngIf="fault.closedout_at && fault.closedout_images && fault.closedout_images.length" [ngStyle]="{'background-color': (i % 2) ? 'none' : 'rgba(0, 0, 0, 0.05)'}">
                                            <td colspan="5">
                                                <pop-up-image-viewer [alt]="'Image'" [imgArray]="fault.closedout_images || []"></pop-up-image-viewer>
                                            </td>
                                        </tr>
                                    </ng-container>
                                </ng-template>
                                </tbody>
                            </table>
                            </div>
                        </ng-template>
                    </li>
                    <li [ngbNavItem]="4">
                        <a ngbNavLink class="nav-a" (click)="toggler(4)">QR Code</a>
                        <ng-template ngbNavContent>
                            <qrcode-generator [qrData]="getQRCodeString(assetVehicle)" [fileName]="getFileName(assetVehicle)" elementType="canvas"></qrcode-generator>
                        </ng-template>
                    </li>
                </ul>
                <div [ngbNavOutlet]="nav" class="col-sm-12 my-2 pt-2 nav-panel"></div>
            </div>
        </div>
        <div *ngIf="assetActivityLogs && assetActivityLogs?.length && activeNav === 1" class="materialRow table-sm">
            <p class="mb-1"><strong>Activity Log:</strong></p>
            <table class="table table-sm table-bordered mb-0">
                <thead>
                <tr>
                    <th class="tr-bg-dark-color"><strong>Date & Time</strong> </th>
                    <th class="tr-bg-dark-color"><strong>Activity</strong> </th>
                    <th class="tr-bg-dark-color"><strong>Activity By</strong></th>
                </tr>
                </thead>
                <tbody>
                <ng-container *ngFor="let t of assetActivityLogs">
                    <tr *ngIf="t.timestamp">
                        <td>{{ dayjsFormat(t.timestamp) }}</td>
                        <td>{{ t.type }}</td>
                        <td>{{ t.name }}</td>
                    </tr>
                </ng-container>
                </tbody>
            </table>
        </div>
</i-modal>

<ng-template #dailyInspectionOptionsHtml let-c="close" let-d="dismiss">
    <div class="modal-header">
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body text-center">
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th class="vertical-align-middle">
                            Week Commencing
                        </th>
                        <th class="vertical-align-middle">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let row of assetVehicle?.availableMondays">
                        <td class="vertical-align-middle">{{row?.date}}</td>
                        <td class="vertical-align-middle">
                            <button class="btn btn-sm btn-outline-primary mr-1 pl-1 pr-1 pt-0 pb-0" (click)="downloadWeeklyInspection(row.date, 'html', assetVehicle?.id)"><i class="fa fa-search"></i></button>
                            <button class="btn btn-sm btn-outline-primary pl-1 pr-1 pt-0 pb-0" (click)="downloadWeeklyInspection(row.date, 'pdf', assetVehicle?.id)"><i class="fa fa-download"></i></button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</ng-template>

<i-modal #archivedVehicleListRef [title]="('Total Archived Vehicle (' + archivedVehicleRecords.length + ')')" size="xl"
    [showCancel]="false" [showFooter]="false" (onCancel)="closeArchiveModal()">
        <div class="card-body">
            <div class="col-12" *ngIf="archivedTaggedOwners.length || archivedVehicleRecords.length">
                <search-with-filters #searchComponentRef (searchEmitter)='searchFunction($event, true)' (filterEmitter)="onFilterSelection($event, true)" [filterData]="archiveFilterData"></search-with-filters>
            </div>
            <div *ngIf="showModal && archivedVehicleRecords.length else notFound" class="table-responsive-sm pl-3 pr-3 min-h-250">
                <ngx-datatable #archiveTable class="bootstrap table table-hover ngx-datatable-row-w sm-pager-view ngx-datatable-custom-h asset-archive-table"
                               [rows]="archivedVehicleRecords ? archivedVehicleRecords : []"
                               [columns]="[
                                   {name:'ID', prop:'vehicle_id', sortable: false, headerClass: 'py-2 font-weight-bold',  width: 80, cellClass: 'py-1 table-cell-text'},
                                   {name:'Reg./Serial No.', prop:'serial_number', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: serialNo},
                                   {name:'Owner(s)', prop:'tagged_owner', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: taggedOwnerCell},
                                   {name:'Type of vehicle', prop:'type_of_vehicle', sortable: true, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text',cellTemplate: type},
                                   {name:'Arrived on site', prop:'fault_count', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: arrivedOnSite},
                                   {name:'Status', prop:'', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: listStatus},
                                   {name:'Action', prop:'', sortable: false, headerClass: 'py-2 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: action}]"
                               [columnMode]="'force'"
                               [footerHeight]="36"
                               [rowHeight]="'auto'"
                               [externalPaging]="true"
                               [externalSorting]="true"
                               [count]="archivedPage.totalElements"
                               [offset]="archivedPage.pageNumber"
                               [limit]="archivedPage.size"
                               (page)="pageCallback($event, true, true)"
                               [sortType]="'single'"
                               (sort)="sortCallback($event, true)"
                               [scrollbarV]="true"
                               [virtualization]="false"
                               [loadingIndicator]="loadingInlineArchivedAssetVehicles"
                >
                    <ng-template #serialNo let-row="row" let-column="column">
                        {{row.serial_number}}
                    </ng-template>

                    <ng-template #type let-row="row" let-column="column">
                        {{ getVehicleType(row.type_of_vehicle) }}
                    </ng-template>
                    <ng-template #taggedOwnerCell  let-row="row" let-column="column">
                        <div class="text-left">
                            <small>{{ getTaggedOwners(row.tagged_owner) }}</small>
                        </div>
                    </ng-template>
                    <ng-template #arrivedOnSite  let-row="row" let-column="column">
                        <div class="text-left">
                            <small>{{ row.arrived_at ? dayjsFormat(row.arrived_at,false) : ''}}</small>
                        </div>
                    </ng-template>
                    <ng-template #listStatus  let-row="row" let-column="column">
                        <div class="d-flex">
                            <span class="d-flex align-items-center" [ngClass]="{'text-warning': row.approval_pending === 1, 'approved-text': row.approval_pending === 2, 'redText': row.approval_pending === 3}" >{{row.approval_pending_message}}</span>
                        </div>
                    </ng-template>
                    <ng-template #action let-row="row" let-column="column">
                        <div class="d-flex">
                            <button title="View" class="btn btn-sm btn-outline-primary mr-1 d-flex align-items-center"
                                    (click)="viewVehicleOptionModal(row)">
                                <span class="material-symbols-outlined font-md-small x-large-font fw-600">search</span>
                            </button>
                            <button title="Unarchive Vehicle" class="btn btn-sm btn-outline-primary d-flex align-items-center"
                                    (click)="unArchiveVehicleAsset(row.id, true, false)">
                                <span class="material-symbols-outlined font-md-small x-large-font fw-600">undo</span>
                            </button>
                        </div>
                    </ng-template>
                </ngx-datatable>
            </div>
            <ng-template #notFound>
                <div class="min-h-250 d-flex align-items-center">
                    <p class="col-sm text-center" > No vehicles found.</p>
                </div>
            </ng-template>
        </div>
</i-modal>


<block-loader [show]="(downloadingWeeklyInspection)" alwaysInCenter="true" zIndex="1200" showBackdrop="true"></block-loader>

<users-selector-modal
    [users_list]="project_admins"
    [selectedUsers]="vehicle_managers"
    [title]="'Vehicle Managers'"
    [heading]="'Vehicle managers will be notified when new vehicles are added or documents are expiring'"
    (selectedUserIds)="saveVehicleManagers($event)"
    #usersSelectorModal
>
</users-selector-modal>

<fault-closeout
    #faultCloseOutRef
    [faultCloseOutReq]="faultCloseOutReq"
    (onCloseOut)="requestCloseOut($event)">
</fault-closeout>

<asset-decline
    #assetDeclineRef
    [declined_comment]="declined_comment"
    (onDecline)="declineVehicle($event)">
</asset-decline>

<block-loader [alwaysInCenter]="true" [show]="blockLoader" [showBackdrop]="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
