<style>
    .table td {
        vertical-align: middle;
    }

    .table-bordered th, .table-bordered td {
        padding: 0.3rem;
    }
</style>
<div class="d-sm-block d-flex mb-2">
    <action-button
        [newFeatureTitle]="'Build Inspection'"
        (onOpenAddNew)="buildUpdateInspectionModal()"
        [showActionDropdown]="false">
    </action-button>
</div>
<table class="table table-bordered">
    <thead>
    <tr>
        <th class="text-center">Inspection Ref</th>
        <th class="text-center">Inspection Title</th>
        <th class="text-center">Created By</th>
        <th class="text-center">Created</th>
        <th class="text-center">Active</th>
        <th class="text-center">Activate Dashboard</th>
        <th class="text-center">Invites</th>
    </tr>
    </thead>
    <tbody>
    <tr *ngFor="let ibChecklist of (ibChecklists || []); trackBy : trackByRowIndex; let i = index;">
        <td class="text-center">
            {{ employer.company_initial}}{{ibChecklist.record_id }}
        </td>
        <td class="text-center">
            <a class="text-info" href="javascript:void(0)" (click)="editInspectionPopup(ibChecklist, i)">{{ibChecklist.ib_title }}</a>
        </td>
        <td class="text-center">
            {{ ibChecklist.user_ref.first_name}} {{ibChecklist.user_ref.last_name }}
        </td>
        <td class="text-center">
            {{ dayjs(ibChecklist.createdAt).format(AppConstant.fullDateTimeFormat) }}
        </td>
        <td class="text-center">
            <input type="checkbox" class="form-control" style="font-size: 5px;"
                   [checked]="ibChecklist.enabled"
                   (click)="enableInspection($event, ibChecklist)"/>
        </td>
        <td class="text-center">
            <input type="checkbox" class="form-control"
                   [checked]="ibChecklist.dashboard_enabled" style="font-size: 5px;"
                   (click)="enableInspectionDashboard($event, ibChecklist)"/>
        </td>
        <td class="text-center">
            <button title="Invite Managers"
                    class="btn btn-sm btn-outline-primary mr-1"
                    (click)="inviteToInspectionPopup(ibChecklist)">
                <i class="fas fa-paper-plane"></i>
            </button>

            <button *ngIf="ibChecklist.invitees && ibChecklist.invitees.length" title="View Invites"
                    class="btn btn-sm btn-outline-primary"
                    (click)="viewInvitesPopup(ibChecklist)">
                <i class="fas fa-search"></i>
            </button>
        </td>
    </tr>
    </tbody>
</table>
<ng-container *ngIf="showInspectionModal">
    <i-modal #buildInspectionHtml size="md" windowClass="buildInspectionModal" [title]="(ibChecklist && ibChecklist.id) ? 'View Inspection' : 'Build Inspection'" [cancelBtnText]="(ibChecklist.id) ? 'Close': 'Cancel'"
        [rightPrimaryBtnTxt]="(!ibChecklist.id) ? 'Create' : 'Update'" (onClickRightPB)="(!ibChecklist.id) ? statusConfirmationPopup(buildInspectionForm, $event) : callUpdateIBChecklist(ibChecklist.id, ibChecklist, $event)"
        [rightPrimaryBtnDisabled]="isInspectionBtnDisable" (onCancel)="closeInspectionBuilderModal()">
            <form novalidate #buildInspectionForm="ngForm">
                <div class="form-group">
                    <label class="font-weight-bold">
                        <span *ngIf="(ibChecklist && ibChecklist.id) else addTitleLabel">Inspection Title:</span>
                        <ng-template #addTitleLabel>
                            <span>Inspection Title:<small class="required-asterisk">*</small></span>
                        </ng-template>
                    </label>

                    <div class="input-group mb-3">
                        <input type="text" class="form-control" placeholder="Inspection Title" name="ib_title"
                               required [(ngModel)]="ibChecklist.ib_title" (ngModelChange)="updateInspectionBtnVisibility()">
                    </div>
                </div>
                <div class="form-group">
                    <fieldset [disabled]="(ibChecklist.id)">
                        <label class="font-weight-bold">
                            <span *ngIf="(ibChecklist && ibChecklist.id) else addScoringLabel">Selected scoring system:</span>
                            <ng-template #addScoringLabel>
                                <span>Choose your scoring system:<small class="required-asterisk">*</small></span>
                            </ng-template>
                        </label>
                        <ng-template ngFor let-item [ngForOf]="scoringSystem" let-scoreIndex="index" >
                            <div class="input-group d-inline-block">
                                <div class="custom-control custom-radio d-inline-block">
                                    <input type="radio" class="custom-control-input" [value]="item.type" name="score_type"
                                           [id]="'scoreType_'+item.type" [(ngModel)]="ibChecklist.score_type" required (click)="scoreTypeSelector(item.type)">
                                    <label class="custom-control-label" [for]="'scoreType_'+item.type">{{item.title}}</label>
                                </div>
                                <span *ngIf="ibChecklist.id && item.has_rating_point && ibChecklist.score_type && item.type == ibChecklist.score_type" class="float-right">
                                    <i>Below indicate a number of points to award for each rating</i>
                                </span>
                            </div>
                            <div class="input-group d-inline-block" *ngIf="!ibChecklist.id && ibChecklist.score_type && item.type == ibChecklist.score_type">
                                <div class="d-inline-block custom-control custom-checkbox ml-4">
                                    <input type="checkbox" class="custom-control-input" [name]="'has_rating_point_'+scoreIndex"
                                        [id]="'has_rating_point_'+scoreIndex" [(ngModel)]="item.has_rating_point" (click)="ratingPointChanged(item)">
                                    <label class="custom-control-label" [for]="'has_rating_point_'+scoreIndex">Add points system </label>
                                </div>
                                 <span *ngIf="item.has_rating_point && ibChecklist.score_type && item.type == ibChecklist.score_type" class="float-right">
                                    <i>Below indicate a number of points to award for each rating</i>
                                </span>
                            </div>
                            <ul class="list-group" *ngIf="activateCustomScoring && (item.type == scoringType.TrafficLight)">
                                <li *ngFor="let customField of customScoringFields; index as i;" class="list-group-item pt-1 pb-1 pr-0" style="border: 0px; padding-left: 24px;">
                                    <div class="input-group scoreField">
                                        <div class="input-group-prepend mr-1" style="margin: auto;">
                                            <i class="fas fa-circle" [ngStyle]="{'color': customField.color}"></i>
                                        </div>
                                        <input type="text" class="form-control mr-2" style="width: 60%" [name]="'option_'+i" [(ngModel)]="customField.value" [placeholder]="customField.placeholder" required (ngModelChange)="isInvalidValue(customField, i)">
                                        <input *ngIf="item.has_rating_point" type="number" class="form-control" [name]="'option_point'+i" (input)="isInvalid(item)" required [(ngModel)]="item.rating_point[i]">
                                    </div>
                                    <div *ngIf="scoringSystemForm.validError[i]?.value && scoringSystemForm.errorMessage[i]?.value?.length" class="alert alert-danger col-10 mb-0 ml-3 mt-1">
                                        {{scoringSystemForm.errorMessage[i].value}}
                                     </div>
                                    <div *ngIf="item.has_rating_point && scoringSystemForm.validError[i]?.rating && scoringSystemForm.item_type[i] == item.type" class="alert alert-danger col-10 mb-0 ml-3 mt-1">
                                        {{scoringSystemForm.errorMessage[i]?.rating}}
                                     </div>
                                </li>
                            </ul>
                            <ul class="list-group" *ngIf="item.type == scoringType.Rating321 && ibChecklist.score_type == scoringType.Rating321">
                                <li *ngFor="let value of item.values; index as i;" class="list-group-item pt-1 pb-1 pr-0" style="border: 0px; padding-left: 24px;">
                                    <div class="input-group scoreField">
                                        <div class="input-group-prepend mr-1" style="margin: auto;">
                                            <i class="fas fa-circle" [ngStyle]="{'color': item.colors[value]}"></i>
                                        </div>
                                        <input type="text" class="form-control mr-2" style="width: 60%" [name]="'option_'+i" [value]="value" disabled>
                                        <input *ngIf="item.has_rating_point" type="number" class="form-control" [name]="'option_point'+i" (input)="isInvalid(item)" required [(ngModel)]="item.rating_point[i]">
                                    </div>
                                    <div *ngIf="item.has_rating_point && scoringSystemForm.validError[i]?.rating && scoringSystemForm.item_type[i] == item.type" class="alert alert-danger col-10 mb-0 ml-3 mt-1">
                                        {{scoringSystemForm.errorMessage[i]?.rating}}
                                     </div>
                                </li>
                            </ul>
                            <ul class="list-group" *ngIf="item.type == scoringType.Satisfactory && ibChecklist.score_type == scoringType.Satisfactory">
                                <li *ngFor="let value of item.values; index as i;" class="list-group-item pt-1 pb-1 pr-0" style="border: 0px; padding-left: 24px;">
                                    <div class="input-group scoreField">
                                        <div class="input-group-prepend mr-1" style="margin: auto;">
                                            <i class="fas fa-circle" [ngStyle]="{'color': item.colors[value]}"></i>
                                        </div>
                                        <input type="text" class="form-control mr-2" style="width: 60%" [name]="'option_'+i" [value]="value" disabled>
                                        <input *ngIf="item.has_rating_point" type="number" class="form-control" [name]="'option_point'+i" (input)="isInvalid(item)" required [(ngModel)]="item.rating_point[i]">
                                    </div>
                                    <div *ngIf="item.has_rating_point && scoringSystemForm.validError[i]?.rating && scoringSystemForm.item_type[i] == item.type" class="alert alert-danger col-10 mb-0 ml-3 mt-1">
                                        {{scoringSystemForm.errorMessage[i]?.rating}}
                                    </div>
                                </li>
                            </ul>
                        </ng-template>
                    </fieldset>
                </div>

                <div class="form-group">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" name="has_severity"
                               id="has_severity" [(ngModel)]="ibChecklist.severity.has_severity">
                        <label class="custom-control-label font-weight-bold" for="has_severity">Include Severity Rating</label>
                        <div *ngIf="ibChecklist.severity?.has_severity" class="mandatoryCheckbox custom-control custom-checkbox py-1" style="font-size: 14px; font-family: 'Font Awesome 5 Free';">
                            <input type="checkbox" class="custom-control-input" id="severity_mandatory" name="severity_mandatory"
                                    #isMandatory="ngModel" [(ngModel)]="ibChecklist.severity.is_mandatory" />
                            <label class="custom-control-label" for="severity_mandatory" style="padding-top: 2px;">Make this a mandatory field</label>
                        </div>
                    </div>
                    <table *ngIf="ibChecklist.severity?.has_severity" class="ml-5 table-borderless col-md-8">
                        <tr>
                            <td></td>
                            <td colspan="2">Time to closeout</td>
                        </tr>
                        <tr *ngFor="let item of ibChecklist.severity.options; let i = index;">
                            <td>
                                <label class="vb">{{item.option}}</label>
                            </td>
                            <td style="width: 20%;">
                                <app-duration-picker [stepSize]="1" [(value)]="ibChecklist.severity.options[i]['days']" [isDayDuration]="true"></app-duration-picker>
                            </td>
                            <td valign="middle">
                                <label>days</label>
                            </td>
                        </tr>
                    </table>
                </div>

                <div class="form-group">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" name="has_root_cause" (ngModelChange)="ibChecklist.root_cause.has_root_cause = $event; updateInspectionBtnVisibility()"
                               id="has_root_cause" [(ngModel)]="ibChecklist.root_cause.has_root_cause">
                        <label class="custom-control-label font-weight-bold" for="has_root_cause">Include Root Cause</label>
                        <div  *ngIf="ibChecklist.root_cause?.has_root_cause" class="mandatoryCheckbox custom-control custom-checkbox pt-1 pb-2" style="font-size: 14px; font-family: 'Font Awesome 5 Free';">
                            <input type="checkbox" class="custom-control-input" id="root_cause_mandatory" name="root_cause_mandatory"
                                    #isMandatory="ngModel" [(ngModel)]="ibChecklist.root_cause.is_mandatory" />
                            <label class="custom-control-label" for="root_cause_mandatory" style="padding-top: 2px;">Make this a mandatory field</label>
                        </div>
                    </div>
                    <inspection-root-cause *ngIf="ibChecklist.root_cause?.has_root_cause"
                                           [ibChecklist]="ibChecklist"
                                           (isValidRootCause)="isValidRootCause = $event; updateInspectionBtnVisibility();"
                    >
                    </inspection-root-cause>
                </div>
    
                <div class="form-group">
                    <label class="font-weight-bold">Details Fields:</label>
                    <custom-detail-fields-manager [openDetailModal]="openDetailModal" [container_obj]="ibChecklist" [fields_key]="'detail_fields'" (validityChanged)="detailFieldValidity= $event; 
                    updateInspectionBtnVisibility();"></custom-detail-fields-manager>
                </div>
    
                <div class="form-group">
                    <fieldset>
                        <label class="font-weight-bold">Checklist:<small class="required-asterisk">*</small></label>
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" name="has_subheadings"
                                       id="has_subheadings" [(ngModel)]="ibChecklist.has_subheadings" (click)="toggleHasSubheading($event)">
                                <label class="custom-control-label font-weight-bold" for="has_subheadings">Include Sections</label>
                                <i ngbTooltip="An example of section could be 'Documentation' and checklist 'Induction Forms'." class="ml-1 fas fa-info-circle mt-1"></i>
                            </div>
                        </div>
                        <div class="d-block">
                            <div class="col-12 p-0 mb-2 heading-list" dragula="heading" [(dragulaModel)]="ibChecklist.checklist">
                                <ng-template ngFor let-item [ngForOf]="(ibChecklist.checklist || [])" let-i="index" *ngIf="!ibChecklist.has_subheadings" [ngForTrackBy]="trackByChecklist">
                                    <div class="drag-box py-2">
                                        <div class="row mx-0">
                                            <div class="col-1 text-center align-self-center p-0">
                                                <i class="fa fa-bars drag-main" aria-hidden="true"></i>
                                            </div>
                                            <div class="col-10 p-0 align-self-center">
                                                <input type="text" class="form-control" placeholder="Add an item..." [(ngModel)]="ibChecklist.checklist[i].item_que" name="heading_{{i}}" required
                                                #checkItem="ngModel" [inputDuplicateValidator]="'heading'" [parentId]="'global'"
                                                autocomplete="off" (blur)="callUpdateInspection()"
                                                />
                                                <div *ngIf="checkItem.errors?.required && checkItem.touched" class="alert alert-danger mb-1 mt-1 py-1">
                                                    Checklist item is required.
                                                </div>
                                                <div *ngIf="checkItem.errors?.duplicate && checkItem.touched" class="alert alert-danger mb-1 mt-1 py-1">
                                                    Checklist item name must be unique.
                                                </div>
                                            </div>
                                            <div *ngIf="ibChecklist.checklist.length > 1" class="col-1 text-start align-self-center d-flex justify-content-center p-0 mt-1">
                                                <span class="material-symbols-outlined text-danger cursor-pointer align-top" (click)="removeCheckItem($event, i)">
                                                    delete
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </ng-template>
                                <ng-template ngFor let-item [ngForOf]="(ibChecklist.checklist || [])" let-i="index" *ngIf="ibChecklist.has_subheadings">
                                    <div class="drag-box py-2">
                                        <div class="row mx-0 mb-2 ml-1">
                                            <div class="col-1 text-center align-self-center p-0">
                                                <i class="fa fa-bars drag-main" aria-hidden="true"></i>
                                            </div>
                                            <div class="col-10 p-0 align-self-center">
                                                <input type="text" class="form-control" placeholder="Section Name"
                                                [(ngModel)]="item.heading" name="heading_{{item.id}}" required
                                                [inputDuplicateValidator]="'heading'" [parentId]="'global'"
                                                #headItem="ngModel" autocomplete="off" (blur)="callUpdateInspection()"
                                                />
                                                <div *ngIf="headItem.errors?.required && headItem.touched" class="alert alert-danger mb-1 mt-1 py-1">
                                                    Section name is required.
                                                </div>
                                                <div *ngIf="headItem.errors?.duplicate && headItem.touched" class="alert alert-danger mb-1 mt-1 py-1">
                                                    Section name must be unique.
                                                </div>
                                            </div>
                                            <div *ngIf="ibChecklist.checklist.length > 1" class="col-1 text-start align-self-center d-flex justify-content-center p-0 mt-1">
                                                <span class="material-symbols-outlined text-danger cursor-pointer align-top" (click)="removeCheckItem($event, i)">
                                                    delete
                                                </span>
                                            </div>
                                        </div>
                                        <div class="row mx-0 ml-5">
                                            <div class="col-12 p-0 mb-2 sub-heading-list" dragula="subheading" [(dragulaModel)]="item.subheadings">
                                                <ng-template ngFor let-subitem [ngForOf]="(item.subheadings || [])" let-k="index">
                                                    <div class="drag-box py-2 pr-1">
                                                        <div class="row mx-0">
                                                            <div class="col-1 text-center align-self-center p-0">
                                                                <i class="fa fa-bars drag-sub" aria-hidden="true"></i>
                                                            </div>
                                                            <div class="col-10 p-0 align-self-center">
                                                                <input type="text" class="form-control" placeholder="Checklist Item"
                                                                [(ngModel)]="subitem.item_que"
                                                                name="question_{{item.id}}_{{subitem.item_id}}"
                                                                required
                                                                [inputDuplicateValidator]="'checklist'" [parentId]="item.id"
                                                                #checkItem="ngModel" autocomplete="off" (blur)="callUpdateInspection()"
                                                                />
                                                                <div *ngIf="checkItem.errors?.required && checkItem.touched" class="alert alert-danger mb-1 mt-1 py-1">Checklist item is required.</div>
                                                                <div *ngIf="checkItem.errors?.duplicate" class="alert alert-danger mb-1 mt-1 py-1">
                                                                    Checklist item must be unique.
                                                                </div>
                                                            </div>
                                                            <div *ngIf="item.subheadings.length > 1" class="col-1 text-start align-self-center d-flex justify-content-center pl-1 p-0 mt-1">

                                                                <span class="material-symbols-outlined text-danger cursor-pointer align-top" (click)="removeSubItem($event, i, k)">
                                                                    delete
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </ng-template>
                                            </div>
                                            <div class="col-12 p-0 py-1">
                                                <span class="text-primary cursor-pointer large-font" (click)="addSubItem(i, item.id)"><i class="fa fa-plus-circle"></i> Add Checklist Item</span>
                                            </div>
                                        </div>
                                    </div>
                                </ng-template>
                            </div>
                            <div class="input-group">
                                <span class="text-primary cursor-pointer" (click)="addCheckItem()"><i class="fa fa-plus-circle"></i> Add Section</span>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </form>
    </i-modal>
</ng-container>

<i-modal #inviteToInspectionHtml size="lg" title="Inspection invites will be sent to all project Nominated Managers" [rightPrimaryBtnDisabled]="!sendInviteToInspectionForm.valid"
    rightPrimaryBtnTxt="Send" (onCancel)="resetAllProjectSelectedVal()" (onClickRightPB)="sendInviteToInspectionRequest(sendInviteToInspectionForm, $event)">
        <form novalidate #sendInviteToInspectionForm="ngForm">
            <div class="form-group">
                <label><strong>Select the project/s:</strong><small class="required-asterisk">*</small></label>
                <div class="col-md-12 p-0 mb-2">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" name="has_all_selected"
                               id="has_all_selected" [checked]="hasAllProjectSelected()" (click)="toggleAllProjectSelection($event)">
                        <label class="custom-control-label font-weight-bold" for="has_all_selected">Select All</label>
                    </div>
                </div>
                <div class="input-group mb-3">
                    <table class="table table-bordered" style="font-size: 12px;">
                        <thead>
                        <tr>
                            <th class="text-center">Project</th>
                            <th class="text-center">Include</th>
                            <th class="text-center">Project</th>
                            <th class="text-center">Include</th>
                            <th class="text-center">Project</th>
                            <th class="text-center">Include</th>
                            <th class="text-center">Project</th>
                            <th class="text-center">Include</th>
                        </tr>
                        </thead>
                        <tbody>
                        <ng-container *ngFor="let project of (companyAdditionalProjects || []); trackBy : trackByRowIndex; let i = index;">
                            <tr *ngIf="i % 4 == 0">
                                <td class="text-center">{{ project.name }}</td>
                                <td class="text-center">
                                    <input type="checkbox" class="cursor-pointer" [checked]="selectedProjects.includes(project.id)"
                                           (change)="changeSelectedProjects($event, project)"/>
                                </td>
                                <td class="text-center">
                                    {{ i < companyAdditionalProjects.length - 1 ? companyAdditionalProjects[i + 1].name : ""}}
                                </td>
                                <td class="text-center">
                                    <input type="checkbox" class="cursor-pointer" *ngIf="i < companyAdditionalProjects.length - 1" [checked]="selectedProjects.includes(companyAdditionalProjects[i + 1].id)"
                                           (change)="changeSelectedProjects($event, companyAdditionalProjects[i + 1])"/>
                                </td>
                                <td class="text-center">
                                    {{ i < companyAdditionalProjects.length - 2 ? companyAdditionalProjects[i + 2].name : ""}}
                                </td>
                                <td class="text-center">
                                    <input type="checkbox" class="cursor-pointer" *ngIf="i < companyAdditionalProjects.length - 2" [checked]="selectedProjects.includes(companyAdditionalProjects[i + 2].id)"
                                           (change)="changeSelectedProjects($event, companyAdditionalProjects[i + 2])"/>
                                </td>
                                <td class="text-center">
                                    {{ i < companyAdditionalProjects.length - 3 ? companyAdditionalProjects[i + 3].name : ""}}
                                </td>
                                <td class="text-center">
                                    <input type="checkbox" class="cursor-pointer" *ngIf="i < companyAdditionalProjects.length - 3" [checked]="selectedProjects.includes(companyAdditionalProjects[i + 3].id)"
                                           (change)="changeSelectedProjects($event, companyAdditionalProjects[i + 3])"/>
                                </td>
                            </tr>
                        </ng-container>
                        <tr>
                            <input type="text" class="d-none" required #selectAtleastOne="ngModel" [ngModel]="isSelectAtleastOneProject()" name="is_project_select"/>
                        </tr>
                        </tbody>
                    </table>
                    <div class="alert alert-danger" [hidden]="(selectAtleastOne.valid)">Please select at least 1 project.</div>
                </div>
            </div>
            <input type="hidden" name="ibChecklist" id="ibChecklist"
                   [(ngModel)]="ibChecklist"/>
        </form>
</i-modal>

<i-modal #viewInviteesHtml size="lg" [title]="'Inspection invites sent - ' + ibChecklist.ib_title" [showCancel]="false" rightPrimaryBtnTxt="Done" (onClickRightPB)="closeViewInvitesModal($event)">
        <table class="table table-bordered" style="font-size: 12px;">
            <thead>
            <tr>
                <th class="text-center" style="width: 35%; background: #f1f1f1;">Project</th>
                <th class="text-center" style="width: 20%; background: #f1f1f1;">Invite Sent</th>
                <th class="text-center" style="width: 40%; background: #f1f1f1;">Invites sent to</th>
            </tr>
            </thead>
            <tbody>
            <ng-container *ngFor="let inviteInfo of (inspectionInvitees || []); trackBy : trackByRowIndex; let i = index;">
                <tr>
                    <td class="text-center" style="width: 35%">{{ inviteInfo.project }}</td>
                    <td class="text-center" style="width: 20%">
                        {{ dayjs(inviteInfo.sent_at).format(AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS) }}
                    </td>
                    <td class="text-center p-0" style="width: 40%;">
                        <ul class="list-group list-group-flush m-0">
                            <li *ngFor="let invitee of (inviteInfo.invitees || [])" class="list-group-item p-1">{{ invitee }}</li>
                        </ul>
                    </td>
                </tr>
            </ng-container>
            </tbody>
        </table>
</i-modal>
<block-loader [show]="processingLoader" [showBackdrop]="true" [alwaysInCenter]="true" #loading></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
