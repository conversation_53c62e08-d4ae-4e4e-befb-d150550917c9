import {Component, OnInit, TemplateRef, ViewChild} from "@angular/core";
import {AuthService, Common, CreateEmployer, PermitTemplate, PermitService, User, TimeUtility, UserService, ToastService} from "@app/core";
import {HttpParams} from "@angular/common/http";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ActivatedRoute} from "@angular/router";
import {PDFButton, PDFDocument, PDFTextField, PDFSignature} from "pdf-lib";
import * as dayjs from 'dayjs';
import {AppConstant} from "@env/environment";
import {DragulaService} from "ng2-dragula";
import { AssetsUrl, GenericConfirmationModalComponent, IModalComponent } from '@app/shared';

@Component({
    selector: 'permit-template',
    templateUrl: './permit-template.component.html',
    styleUrls: ['./permit-template.component.scss']
})

export class PermitTemplateComponent implements OnInit {
    commonModel = new Common();
    displayDateTimeFormat: string = AppConstant.dateTimeFormat_D_MMM_YYYY_HH_MM_SS;
    //META_PERMIT_TYPE: Array<string> = ['Permit to Dig', 'Ladder Permit', 'Permit to Break Ground', 'Permit to Lift'];
    ALLOWED_MIME_TYPE: Array<string> = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']; //pdf, doc
    META_DAYS: Array<any> = [];
    META_HOURS: Array<any> = [];
    isDataLoading: boolean = false;
    requestProcessing: boolean = false;
    permitTemplates: Array<PermitTemplate> = [];
    paginationData = new Common();
    page = this.paginationData.page;
    tableOffset: number = 0;
    permitSearchStr: string;
    authUser$: User;
    companyResolverResponse: any;
    employer: CreateEmployer = {};
    permitTemplate: PermitTemplate = new PermitTemplate;
    expireDay: number = null;
    expireHour: number = null;
    showAdditionalSection: boolean = false;
    fillablePdfFormFields: Array<any> = [];
    sectionFillablePdfFormFields: any = {};
    fillablePdfSelectField: any = {};
    fillablePdfFormFieldTypes: Array<object> = [{"label":"Autofill Field","key":"data_field"},{"label":"Active RAMS Dropdown List","key":"active_rams"},{"label":"Company Dropdown List","key":"company_dropdown_list"},{"label":"Date Selector","key":"date"},{"label":"Date/Time Selector","key":"datetime_selector"},{"label":"Dropdown","key":"selectbox"},{"label":"Location","key":"location"},{"label":"Multi-Select List","key":"multi_select"},{"label":"Multiline Textbox","key":"textarea"},{"label":"Number","key":"number"},{"label":"Radio Y/N","key":"radio_yn"},{"label":"Radio Y/N/NA","key":"radio_ynna"},{"label":"Textbox","key":"textbox"},{"label":"Time Selector","key":"time_selector"}];
    predefinedDataFields: Array<any> = [{"key":"company_of_requestor","label":"Company of requestor"},{"key":"company_of_signatory","label":"Company of signatory"},{"key":"date_of_request","label":"Date of request"},{"key":"datetime_of_request","label":"Date/Time of request"},{"key":"job_role_of_requestor","label":"Job role of requestor"},{"key":"job_role_of_signatory","label":"Job role of signatory"},{"key":"name_of_requestor","label":"Name of requestor"},{"key":"name_of_signatory","label":"Name of signatory"},{"key":"permit_expiry_date","label":"Permit Expiry Date"},{"key":"permit_expiry_datetime","label":"Permit Expiry Date/Time"},{"key":"permit_expiry_time","label":"Permit Expiry Time"},{"key":"permit_record_id","label":"Permit ID"},{"key":"permit_ref","label":"Permit Reference No."},{"key":"permit_start_date","label":"Permit Start Date"},{"key":"permit_start_datetime","label":"Permit Start Date/Time"},{"key":"permit_start_time","label":"Permit Start Time"},{"key":"permit_status","label":"Permit Status"},{"key":"permit_type","label":"Permit Type"},{"key":"project_name","label":"Project Name"},{"key":"project_contractor","label":"Project/Contract Number"},{"key":"project_postcode","label":"Site Postcode"},{"key":"sign_off_date","label":"Sign-off date"},{"key":"sign_off_datetime","label":"Sign-off date/time"},{"key":"sign_off_time","label":"Sign-off time"},{"key":"time_of_request","label":"Time of request"}];
    fillable_pdf_file: any = {};
    signatures: Array<any> = [];
    closeoutSignatures: Array<{
        "collapse": boolean,
        "hide_info": boolean,
        "field_name": string,
        "declaration": string,
        "field_label": string,
        "is_closeout": boolean,
        "link_fields": Array<string>,
        "link_sections": Array<number>,
        "sign_number": number,
        "is_requestor": boolean,
        "is_closeout_requestor": boolean
    }> = [];
    field_sections: Array<any> = [{'section_id': this.commonModel.getUniqueId(), 'title': '', 'is_disabled': false, show_options: false}];
    selectedSectionFields: Array<string> = [];
    hasAssignedSectionToFields: boolean = true;
    AssetsUrlSiteAdmin = AssetsUrl.siteAdmin;
    contentToVisible: string = 'unassigned';
    selectedLinkFields: Array<string> = [];
    selectedLinkSections: Array<string> = [];
    signatureTypes: Array<string> = ['signature', 'esignature'];
    fontSizeOptions: Array<number> = [];
    isValidForSelected: boolean = true;
    @ViewChild('confirmationModalRef') private confirmationModalRef: GenericConfirmationModalComponent;
    take_register_options: Array<any> = [{"value":"creating_permit","label":"Submitting a permit request"},{"value":"permit_approved","label":"Permit has been approved before starting works"}];
    register_signature_options: Array<any> = [{"value":"not_required","label":"Not required"},{"value":"optional","label":"Optional"},{"value":"mandatory","label":"Mandatory"}];
    mandatory_attachments_title: Array<any> = [];
    hasRequestor = false;
    titleRegex = /^[!@#$%^&*()_+={}\[\]:;'<>,.?/\\|~\w\s]+$/;
    closeoutRequestorIndex: number = undefined;
    constructor(
        private modalService: NgbModal,
        private permitService: PermitService,
        private activatedRoute: ActivatedRoute,
        private authService: AuthService,
        private timeUtility: TimeUtility,
        private dragulaService: DragulaService,
        private userService: UserService,
        private toastService: ToastService,
    ) {
        this.META_DAYS = this.createDaysArray();
        this.META_HOURS = this.createHoursArray();
        for (let i = 8; i <= 20; i++) {
            this.fontSizeOptions.push(i);
        }

        /*this.resourceService.getInnDexSettingByName('permit_type_en_gb').subscribe((data:any) => {
            if(data.success && data.record && data.record.value) {
                this.META_PERMIT_TYPE = data.record.value;
            } else {
                alert('Something went wrong while fetching permit types.');
            }
        });*/

        this.authService.authUser.subscribe(data => {
            if (data && data.id) {
                this.authUser$ = data;
            }
        });

        this.companyResolverResponse = this.activatedRoute.snapshot.data.companyResolverResponse;
        this.employer = this.companyResolverResponse.company;
        this.fetchPermitTemplates();
    }

    ngOnInit() {}

    ngAfterViewInit() {
        this.dragulaService.destroy('signaturefields');
        this.dragulaService.createGroup('signaturefields', {
            moves: (el: any, container: any, handle: any) => {
                if (el.classList.contains('donot-drag')) {
                    return false;
                }
                return true;
            }
        });

        this.dragulaService.drop('signaturefields').subscribe(() => {
            this.linkFieldsSelected(true);
        });


        this.dragulaService.drop('closeoutSignatures').subscribe(() => {
            this.linkFieldsSelected(true);
        });

        this.dragulaService.destroy('closeoutSignatures');
        this.dragulaService.createGroup('closeoutSignatures', {
            moves: (el: any, container: any, handle: any) => {
                if (el.classList.contains('donot-drag')) {
                    return false;
                }
                return true;
            }
        });
    }

    sectionDragulaInit() {
        if (this.field_sections.length === 1) return;
        for (let i = 0; i < this.field_sections.length; i++) {
            if (this.field_sections[i].section_id && !this.field_sections[i].title) {
                continue;
            }
            let sectionId = this.field_sections[i].section_id;
            this.dragulaService.destroy('fillablepdffields_'+sectionId);
            this.dragulaService.createGroup('fillablepdffields_'+sectionId, {
                accepts: (el, target, source, sibling) => {
                    // To avoid dragging from one box to another box
                    return target.id == source.id;
                },
                revertOnSpill: true
            });

            this.dragulaService.drop('fillablepdffields_'+sectionId).subscribe(({name, el, target, source, sibling}) => {
                let currentSectionId = el.getAttribute('section_id')
                let itemFieldId = el.id;
                let nextItemFieldId = (sibling) ? sibling.id : undefined;
                let findOldIndex = this.sectionFillablePdfFormFields[currentSectionId].findIndex(item => item.field_id === +itemFieldId);
                let findNextItemIndex = this.sectionFillablePdfFormFields[currentSectionId].findIndex(item => item.field_id === +nextItemFieldId);
                let element = this.sectionFillablePdfFormFields[currentSectionId].splice(findOldIndex, 1)[0];
                if (findNextItemIndex === -1) {
                    let lastItemIndex = this.sectionFillablePdfFormFields[currentSectionId].map(el => el.section_id).lastIndexOf(+currentSectionId);
                    this.sectionFillablePdfFormFields[currentSectionId].splice(lastItemIndex+1, 0, element);
                } else {
                    findNextItemIndex = (findNextItemIndex > findOldIndex) ? findNextItemIndex - 1 : findNextItemIndex;
                    this.sectionFillablePdfFormFields[currentSectionId].splice(findNextItemIndex, 0, element);
                }
            });
        }
    }

    searchFilter(data){
        this.permitSearchStr = data.search.toLowerCase();
        this.page.pageNumber = 0;
        this.fetchPermitTemplates();
    }

    dayjs(n: number, format?: any) {
        return dayjs(n, format);
    };

    pageCallback(pageInfo: {count?: number, pageSize?: number, limit?: number, offset?: number }) {
        this.page.pageNumber = pageInfo.offset;
        this.fetchPermitTemplates();
    }

    openModal(modalRef, size = 'sm', windowClass = "") {
        return this.modalService.open(modalRef, {
            backdropClass: 'light-blue-backdrop',
            backdrop: 'static',
            keyboard: false,
            centered: true,
            size,
            windowClass: windowClass
        });
    }

    createDaysArray() {
        const days = [{ name: "0 Days", value: 0 }, { name: "1 Day", value: 1 }];
        for (let i = 2; i <= 31; i++) {
            days.push({ name: `${i} Days`, value: i });
        }
        return days;
    }

    createHoursArray() {
        const hours = [{ name: "0 Hours", value: 0 }, { name: "1 Hour", value: 1 }];
        for (let i = 2; i <= 23; i++) {
            hours.push({ name: `${i} Hours`, value: i });
        }
        return hours;
    }

    refDocUploadDone($event) {
        if($event && $event.userFile) {
            this.permitTemplate.ref_docs.splice(1, 0,...$event.userFile);
            this.permitTemplate.ref_docs[0] = {};
        }
    }

    deleteRefDoc($event, index) {
        if($event && $event.userFile && $event.userFile.id) {
            this.permitTemplate.ref_docs.splice(index, 1);
        }
    }

    @ViewChild('permitTemplatesModal')
    private permitTemplatesModalRef: IModalComponent;
    createNewPermitModal() {
        this.expireDay = null;
        this.expireHour = null;
        this.permitTemplate = new PermitTemplate();
        this.permitTemplate.ref_docs = [{}];
        this.permitTemplate.font_size = 12;
        this.resetFillablePdf();
        this.permitTemplatesModalRef.open();
    }

    resetFillablePdf() {
        this.hasRequestor = false;
        this.fillable_pdf_file = {};
        this.fillablePdfFormFields = [];
        this.sectionFillablePdfFormFields = {};
        this.permitTemplate.fillable_pdf_fields = [];
        this.permitTemplate.fillable_pdf_ref = null;
        this.signatures = [];
        this.closeoutSignatures = [];
        this.closeoutRequestorIndex = undefined;
        this.selectedLinkFields = [];
        this.selectedSectionFields = [];
        this.selectedLinkSections = [];
        this.field_sections = [{'section_id': this.commonModel.getUniqueId(), 'title': '', 'is_disabled': false, show_options: false}];
    }

    checkSectionValidation() {
        this.hasAssignedSectionToFields = true;
        let field_sections = (this.field_sections || []).filter(section => section.title);
        if (field_sections.length) {
            this.permitTemplate.fillable_pdf_fields = [];
            for (let i = 0; i < field_sections.length; i++) {
                let sectionId = field_sections[i].section_id;
                let sectionFields = this.sectionFillablePdfFormFields[sectionId].filter(field => field.section_id === sectionId);
                this.permitTemplate.fillable_pdf_fields.push(...sectionFields);
            }
            this.permitTemplate.fillable_pdf_fields.push(...(this.fillablePdfFormFields.filter(field => !field.section_id)));

            let fieldWithoutSection = [];
            this.permitTemplate.fillable_pdf_fields = this.permitTemplate.fillable_pdf_fields.map(field => {
                if (!field.section_id && !this.signatureTypes.includes(field.field_type) && !field.linked_with) {
                    fieldWithoutSection.push(field.field_name);
                }
                return field;
            });
            this.hasAssignedSectionToFields = (fieldWithoutSection.length) ? false : true;

            this.fillablePdfFormFields = [...this.permitTemplate.fillable_pdf_fields];
        }
    }

    savePermitTemplate(cb) {
        this.checkSectionValidation();
        if (!this.hasAssignedSectionToFields) {
            const message = 'Please assign section to all fields.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }

        this.permitTemplate.field_sections = this.field_sections.filter(section => section.title);
        if (this.permitTemplate.field_sections.length && this.permitTemplate.field_sections.length != this.selectedLinkSections.length && this.hasRequestor) {
            const message = 'Please link all sections to a signature step to continue.';
            this.toastService.show(this.toastService.types.INFO, message);
            return;
        }

        this.permitTemplate.expire_in = (+this.expireDay || +this.expireHour) ? `P${+this.expireDay}D${+this.expireHour}H` : null;
        this.permitTemplate.mandatory_attachments_title = (this.mandatory_attachments_title).filter(title => title);
        this.permitTemplate.fillable_pdf_fields = this.fillablePdfFormFields.map(field => {
            if (field.field_type === 'data_field') {
                field.is_mandatory = false;
            }
            return field;
        });
        this.permitTemplate.fillable_pdf_ref = this.fillable_pdf_file.id;
        this.permitTemplate.ref_docs = this.permitTemplate.ref_docs.map(({ id }) => id).filter(id => id != null);
        this.permitTemplate.signatures = [...this.signatures, ...this.closeoutSignatures];
        if (!this.permitTemplate.field_sections.length) {
            this.permitTemplate.signatures.map(sign => {
                delete sign.link_sections;
                return sign;
            });
        }
        this.requestProcessing = true;
        this.isDataLoading = true;
        if (this.permitTemplate.id) {
            this.permitService.updatePermitTemplate(this.permitTemplate, this.employer.id, this.permitTemplate.id).subscribe(this.responseHandler.bind(this, cb));
        } else {
            this.permitService.savePermitTemplate(this.permitTemplate, this.employer.id).subscribe(this.responseHandler.bind(this, cb));
        }
    }

    editPermitTemplateModal(permitTemplate) {
        this.selectedLinkSections = [];
        this.hasRequestor = false;
        this.permitTemplate = JSON.parse(JSON.stringify(permitTemplate));
        this.permitTemplate.include_register = (this.permitTemplate.take_register_when) ? true : false;
        this.permitTemplate.ref_docs = [{}, ...this.permitTemplate.ref_docs];
        this.mandatory_attachments_title = ['', ...this.permitTemplate.mandatory_attachments_title];
        this.fillablePdfFormFields = this.permitTemplate.fillable_pdf_fields;
        this.fillable_pdf_file = this.permitTemplate.fillable_pdf_ref;
        this.signatures = JSON.parse(JSON.stringify(this.permitTemplate.signatures));
        this.signatures.map(sign => {
            this.hasRequestor = (!this.hasRequestor) ? sign.is_requestor : this.hasRequestor;
            if (sign.is_requestor) {
                this.selectedLinkSections.push(...(sign.link_sections || []));
            }
            return sign;
        });
        this.closeoutSignatures = this.signatures.filter(sign => (sign.is_closeout || sign.is_closeout_requestor));
        this.signatures = this.signatures.filter(sign => !(sign.is_closeout || sign.is_closeout_requestor));
        let expireIn = this.timeUtility.extractDayAndHour(this.permitTemplate.expire_in);
        this.expireDay = (!isNaN(expireIn.day)) ? expireIn.day : null;
        this.expireHour = (!isNaN(expireIn.hour)) ? expireIn.hour : null;
        this.field_sections = JSON.parse(JSON.stringify(this.permitTemplate.field_sections));
        this.permitTemplate.font_size = this.permitTemplate.font_size || 12;
        this.linkFieldsSelected(false);
        this.hasAssignedSectionToFields = true;
        //show fillable pdf section
        this.permitTemplatesModalRef.open();
        //this.openModal(this.permitTemplatesModalRef, 'xl', "permitModal modalHeightAuto modal_v2 xl-modal");
        this.addFieldSection();
        this.checkSectionValidation();
    }

    responseHandler(cb, out: any) {
        this.isDataLoading = false;
        this.requestProcessing = false;
        if (out.success) {
            if (cb) {
                cb.closeFn();
            }
            this.fetchPermitTemplates();
        } else {
            const message = out.message || 'Failed to complete the request.';
            this.toastService.show(this.toastService.types.ERROR, message, { data: out });
        }
    }

    fetchPermitTemplates() {
        let params = new HttpParams()
            .set('pageNumber', `${this.page.pageNumber}`)
            .set('pageSize', `${this.page.size}`);

        if(this.permitSearchStr) {
            params = params.append('q',`${this.permitSearchStr}`);
        }
        this.isDataLoading = true;
        this.permitService.fetchPermitTemplates(this.employer.id, params).subscribe((data:any) => {
            this.isDataLoading = false;
            if (data.success && data.records) {
                this.permitTemplates = data.records;
                this.page.totalElements = data.totalCount;
            }
        });
    }

    updatePermitTemplateStatus(permitTemplate, $event) {
        let titleAndBtnTxt = (permitTemplate?.is_active) ? 'Disable' : 'Enable';
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: `${titleAndBtnTxt} Permit`,
            title: `Are you sure you want to ${titleAndBtnTxt.toLowerCase()} this permit?`,
            confirmLabel: `${titleAndBtnTxt}`,
            onConfirm: () => {
                this.isDataLoading = true;
                let request = {
                    is_active: !permitTemplate.is_active
                }
                this.permitService.updatePermitTemplateStatus(request, this.employer.id, permitTemplate.id).subscribe(this.responseHandler.bind(this, ''));
            },
            onClose: () => {
                $event.target.checked = !$event.target.checked;
            }
        });
    }

    trackByRowIndex(index, obj) {
        return index;
    }

    addAttachmentTitle(index) {
        this.mandatory_attachments_title = ['', ...this.mandatory_attachments_title];
    }

    removeAttachmentTitle(index) {
        this.mandatory_attachments_title.splice(index, 1);
    }

    deleteFillablePdf($event, forceDelete=false) {
        if($event && $event.userFile && $event.userFile.id) {
            if (forceDelete) {
                this.userService.deleteUserFile($event.userFile).subscribe(data => {
                    if (!data.success) {
                        console.log('Delete API response ', data);
                    } else {
                        this.resetFillablePdf();
                    }
                });
            } else {
                this.resetFillablePdf()
            }
        }
    }

    async uploadFillablePdfDone($event:any) {
        if($event && $event.userFile && $event.userFile.id){
            this.fillable_pdf_file = $event.userFile;
            this.permitTemplate.fillable_pdf_ref = $event.userFile.id;

            // Fetch the PDF with form fields
            const formUrl = $event.userFile.file_url; //'https://pdf-lib.js.org/assets/dod_character.pdf'
            const formBytes= await fetch(formUrl).then((res) => res.arrayBuffer());
            // Load the PDF with form fields
            const pdfDoc = await PDFDocument.load(formBytes);
            // Get two text fields from the form
            const form = pdfDoc.getForm();
            const fields = form.getFields();
            if (!fields.length) {
                this.deleteFillablePdf($event, true);
                const message = 'Please upload valid fillable pdf.';
                this.toastService.show(this.toastService.types.ERROR, message);
                return;
            }
            let tempFillableFields = [];
            fields.forEach(field => {
                let type = field.constructor.name
                let name = field.getName();
                tempFillableFields.push(name);
                //for signature
                if (field instanceof PDFButton || field instanceof PDFSignature) {
                    this.fillablePdfFormFields.push({
                        "field_id": (this.fillablePdfFormFields.length + 1),
                        "field_label": name,
                        "field_name": name,
                        "field_type": (field instanceof PDFButton) ? 'signature' : 'esignature',
                        "is_mandatory": false,
                        "hide_info": true,
                        "collapse": true,
                    });

                    this.signatures.push({
                        field_label: name,
                        declaration: '',
                        field_name: name,
                        sign_number: this.signatures.length + 1,
                        is_requestor: false,
                        is_closeout_requestor: false,
                        is_closeout: false,
                        link_fields: [],
                        link_sections: [],
                        hide_info: false,
                        collapse: false,
                    });
                }

                if (field instanceof PDFTextField) {
                    let textField = form.getTextField(name);
                    if (textField.isMultiline()) {
                        type = 'textarea';
                    } else {
                        type = 'textbox';
                    }

                    this.fillablePdfFormFields.push({
                        "field_id": (this.fillablePdfFormFields.length + 1),
                        "field_label": name,
                        "field_name": name,
                        "field_type": type,
                        "is_mandatory": false,
                        "hide_info": false,
                        "collapse": false,
                    });
                }
            });

            const duplicateFields = tempFillableFields.filter((item, index) => tempFillableFields.indexOf(item) !== index);
            if (duplicateFields.length) {
                this.resetFillablePdf();
                this.deleteFillablePdf($event, true);
                const message = `Got following duplicate fields in the pdf "${duplicateFields.join(',')}", please make correction and upload again.`;
                this.toastService.show(this.toastService.types.WARNING, message);
                return;
            }
        }
    }

    toggleFieldInfo(index, isSignature, sectionId=null) {
        if (isSignature) {
            this.signatures[index].collapse = !this.signatures[index].collapse;
        } else if(!sectionId) {
            this.fillablePdfFormFields[index].collapse = !this.fillablePdfFormFields[index].collapse;
        } else if(sectionId) {
            this.sectionFillablePdfFormFields[sectionId][index].collapse = !this.sectionFillablePdfFormFields[sectionId][index].collapse;
        }
    }

    @ViewChild('customSelectOptionsModal')
    private customSelectOptionsModalRef: TemplateRef<any>;
    manageCustomSelectOption(fieldIndex, sectionId=null) {
        this.fillablePdfSelectField = (sectionId) ? this.sectionFillablePdfFormFields[sectionId][fieldIndex] : this.fillablePdfFormFields[fieldIndex];
        let existingOptions = Object.assign([], this.fillablePdfSelectField.options);
        this.fillablePdfSelectField.options =  [
            {
                'label': '',
            },
            ...((this.fillablePdfSelectField.options || []).filter(option => option.label))
        ];
        let modalReference = this.openModal(this.customSelectOptionsModalRef, 'md', "modalHeightAuto modal_v2");

        modalReference.result.then((result) => {
            this.fillablePdfSelectField.options = (this.fillablePdfSelectField.options || []).filter(option => option.label);
            if (sectionId) {
                this.sectionFillablePdfFormFields[sectionId][fieldIndex] = this.fillablePdfSelectField
            } else {
                this.fillablePdfFormFields[fieldIndex] = this.fillablePdfSelectField;
            }
        }, (reason) => {
            this.fillablePdfSelectField.options = existingOptions;
            if (sectionId) {
                this.sectionFillablePdfFormFields[sectionId][fieldIndex] = this.fillablePdfSelectField
            } else {
                this.fillablePdfFormFields[fieldIndex] = this.fillablePdfSelectField;
            }
        });
    }

    addCustomSelectOption(optionIndex) {
        if(this.fillablePdfSelectField.options && this.fillablePdfSelectField) {
            this.fillablePdfSelectField.options = [
                {
                    "label": '',
                },
                ...((this.fillablePdfSelectField.options || []).filter(option => option.label))
            ];
        }
    }

    removeCustomSelectOption(optionIndex) {
        this.fillablePdfSelectField.options.splice(optionIndex, 1);
    }

    onOptionChange($event, index, field) {
        if (this.signatures[index][field]) {
            this.signatures[index][field] = false;
            $event.target.checked = false;

            //remove section from requestor sign-off if it is ticked-off
            let sectionCount = this.field_sections.filter(section => section.title).length;
            if (field === 'is_requestor' && this.selectedLinkSections.length === sectionCount) {
                this.selectedLinkSections = this.selectedLinkSections.filter(val => !(this.signatures[index].link_sections).includes(val));
                this.signatures[index].link_sections = [];
            }
        } else {
            this.signatures[index][field] = true;
        }

        if (['is_closeout', 'is_closeout_requestor'].includes(field) && this.signatures[index][field]) {
            if(field === 'is_closeout_requestor') {
                this.closeoutRequestorIndex = ($event.target.checked) ? index : undefined;
                this.closeoutSignatures = [this.signatures[index], ...this.closeoutSignatures]; //Closeout Requestor on 1st place
            } else {
                this.closeoutSignatures.push(this.signatures[index]);
            }
            this.signatures.splice(index, 1);
        }

        this.signatures.forEach((sign, index) => sign.sign_number = index + 1);
        this.closeoutSignatures.forEach((sign, index) => sign.sign_number = index + 1);

        this.hasRequestor = (field === 'is_requestor') ? this.signatures[index][field] : this.hasRequestor;
    }

    onCloseoutOptionChange($event, index, field) {
        this.closeoutSignatures[index][field] = false;
        this.signatures = [...this.signatures, this.closeoutSignatures[index]];
        this.closeoutSignatures.splice(index, 1);
        if (field === 'is_closeout_requestor') {
            this.closeoutRequestorIndex = undefined;
        }

        this.signatures.forEach((sign, index) => sign.sign_number = index + 1);
        this.closeoutSignatures.forEach((sign, index) => sign.sign_number = index + 1);
    }

    addFieldSection(sectionId=null) {
        let newlyAddedItem = this.field_sections[1];
        if (sectionId && newlyAddedItem && !this.titleRegex.test(newlyAddedItem.title)) {
            return false;
        }
        this.hasAssignedSectionToFields = false;
        this.field_sections = this.field_sections.map((section, index) => {
            this.sectionFillablePdfFormFields[section.section_id] = [...this.fillablePdfFormFields];
            return { ...section, is_disabled: true, show_options: false };
        });
        this.field_sections.unshift({ 'section_id': this.commonModel.getUniqueId(), 'title': '', 'is_disabled': false, show_options: false });
        this.sectionDragulaInit();

        //adding last added section's id to requestor sign-off
        if (sectionId) {
            let newlyAddedItem = this.field_sections[1];
            this.signatures.map(sign => {
                this.hasRequestor = (!this.hasRequestor) ? sign.is_requestor : this.hasRequestor;
                if (sign.is_requestor) {
                    sign.link_sections = [...(sign.link_sections || []), newlyAddedItem.section_id];
                    this.selectedLinkSections.push(newlyAddedItem.section_id);
                }
                return sign;
            });
        }
    }

    removeFieldSection(index) {
        let sectionId = this.field_sections[index].section_id;
        this.fillablePdfFormFields.map(field => {
            if (field.section_id && field.section_id == sectionId) {
                delete field.section_id;
            }
            return field;
        });
        delete this.sectionFillablePdfFormFields[sectionId];
        this.field_sections.splice(index, 1);
        this.hasAssignedSectionToFields = (this.field_sections.length <= 1);

        //remove section id from sign-offs
        this.signatures.map(sign => {
            sign.link_sections = (sign.link_sections || []).filter(val => val != sectionId);
            this.selectedLinkSections = (this.selectedLinkSections || []).filter(val => val != sectionId);
            return sign;
        });

        //remove section id from sign-offs
        this.closeoutSignatures.map(sign => {
            sign.link_sections = (sign.link_sections || []).filter(val => val != sectionId);
            this.selectedLinkSections = (this.selectedLinkSections || []).filter(val => val != sectionId);
            return sign;
        });
    }

    enableInputField(index) {
        if (!this.field_sections[index].is_disabled && !this.titleRegex.test(this.field_sections[index].title)) {
            return false;
        }
        this.field_sections[index].is_disabled = !this.field_sections[index].is_disabled;
    }

    toggleFieldSelector(index, sectionId) {
        this.field_sections[index].show_options = !this.field_sections[index].show_options;
        this.selectedSectionFields = [];
        if (this.field_sections[index].show_options) {
            this.selectedSectionFields = this.sectionFillablePdfFormFields[sectionId].reduce((arr, field) => {
                if (field.section_id && (field.section_id == this.field_sections[index].section_id) && field.field_name) {
                    arr.push(field.field_name)
                }
                return arr;
            }, []);
        }
    }

    toggleSectionField(field_name, sectionId) {
        const index = this.selectedSectionFields.indexOf(field_name);
        if (index === -1) {
            this.selectedSectionFields.push(field_name)
        } else {
            this.selectedSectionFields.splice(index, 1);
            let field_index = (this.sectionFillablePdfFormFields[sectionId]).findIndex(field => field.field_name === field_name);
            delete this.sectionFillablePdfFormFields[sectionId][field_index].section_id;
            this.hasAssignedSectionToFields = false;
        }
    }

    assignSectionToFields(sectionIndex, sectionId) {
        this.sectionFillablePdfFormFields[sectionId] = this.sectionFillablePdfFormFields[sectionId].map(field => {
            if (this.selectedSectionFields.includes(field.field_name)) {
                field.section_id = sectionId;
            }
            return field;
        });
        this.field_sections[sectionIndex].show_options = false;
        this.selectedSectionFields = [];
        this.checkSectionValidation();
    }

    moveSectionOneDown(index) {
        if (this.field_sections.length - 2 >= index) {
            console.log("Valid move, index: ", index);
            const temp = this.field_sections[index];
            this.field_sections[index] = this.field_sections[index+1];
            this.field_sections[index+1] = temp;
            return;
        }
        console.log("Invalid move, index: ", index);
    }

    showFields(contentToVisible) {
        this.contentToVisible = contentToVisible;
    }

    linkFieldsSelected(isDragDrop) {
        this.selectedLinkFields = [];
        this.selectedLinkSections = [];
        let signOfforder = 1;
        let closeoutSignOfforder = 1;

        if(isDragDrop && this.closeoutRequestorIndex) {
            this.closeoutSignatures.forEach((sign, index) => {
                sign.is_closeout_requestor = index === 0;
                sign.is_closeout = index !== 0;
            });
        }

        let signArray = [...this.signatures, ...this.closeoutSignatures].map((sign, index) => {
            this.selectedLinkFields.push(...sign.link_fields);
            if (sign.link_sections && sign.link_sections.length) {
                this.selectedLinkSections.push(...sign.link_sections);
            }

            sign.is_requestor = (isDragDrop) ? false : sign.is_requestor;
            if (!isDragDrop) {
                this.fillablePdfFormFields.map(field => {
                    //reset value in first iteration
                    if (index === 0) {
                        field.field_type = (field.linked_with) ? undefined : field.field_type;
                        //field.data_field = (field.linked_with) ? '' : field.data_field;
                        delete field.linked_with;
                    }

                    if (sign.link_fields && sign.link_fields.length && (sign.link_fields).includes(field.field_name)) {
                        field.field_type = 'data_field';
                        field.is_mandatory = false;
                        field.linked_with = sign.field_name;
                    }
                });
            } else {
                if (sign.is_closeout || sign.is_closeout_requestor) {
                    sign.sign_number = closeoutSignOfforder;
                    closeoutSignOfforder++;
                } else {
                    sign.sign_number = signOfforder;
                    signOfforder++;
                }
            }
            return sign;
        });

        this.closeoutSignatures = signArray.filter(sign => (sign.is_closeout || sign.is_closeout_requestor));
        this.signatures = signArray.filter(sign => !(sign.is_closeout || sign.is_closeout_requestor));
    }

    linkSectionsSelected(signIndex, area='signOff') {
        this.selectedLinkSections = [];
        this.hasRequestor = false;
        [...this.signatures, ...this.closeoutSignatures].map(sign => {
            this.hasRequestor = (!this.hasRequestor) ? sign.is_requestor : this.hasRequestor;
            if (sign.link_sections) {
                this.selectedLinkSections.push(...sign.link_sections);
            }
            return sign;
        });

        //-1 to ignore empty section
        if (this.selectedLinkSections.length === this.field_sections.length-1 && !this.hasRequestor) {
            const message = 'All but one section will be linkable to the signatories.';
            this.toastService.show(this.toastService.types.INFO, message);
            alert("At least 1 section must to be linked to the requestor step");
            let removedSectionId = null;
            //remove last added section id
            if (area === 'signOff') {
                removedSectionId = this.signatures[signIndex].link_sections[this.signatures[signIndex].link_sections.length-1];
                this.signatures[signIndex].link_sections = (this.signatures[signIndex].link_sections || []).slice(0, -1);
            } else {
                removedSectionId = this.closeoutSignatures[signIndex].link_sections[this.closeoutSignatures[signIndex].link_sections.length-1];
                this.closeoutSignatures[signIndex].link_sections = (this.closeoutSignatures[signIndex].link_sections || []).slice(0, -1);
            }

            let sectionIdIndex = this.selectedLinkSections.indexOf(removedSectionId);
            if (removedSectionId && (sectionIdIndex !== -1)) {
                this.selectedLinkSections.splice(sectionIdIndex, 1);
            }
            return false;
        }
    }

    toggleRequireCloseout($event) {
        this.signatures.push(...this.closeoutSignatures);
        this.closeoutSignatures = [];
        this.signatures.map(signature => {
            signature.is_closeout = false;
            signature.is_closeout_requestor = false;
            return signature;
        });
        this.closeoutRequestorIndex = undefined;
    }

    getSectionTitle(sectionId) {
        if (sectionId) {
            let section = this.field_sections.find(section => section.section_id === sectionId);
            return section.title || ''
        }
    }

    changeFieldType(index) {
        this.fillablePdfFormFields[index].is_mandatory = (this.fillablePdfFormFields[index].field_type === 'data_field') ? false : this.fillablePdfFormFields[index].is_mandatory;
    }

    /*closePermitTemplateModal() {
        this.confirmationModalRef.openConfirmationPopup({
            headerTitle: 'Quit',
            title: `Are you sure you want to quit?  All of your progress will be lost.`,
            confirmLabel: 'Quit',
            onConfirm: () => {
                this.permitTemplatesModalRef.close();
            }
        });
    }*/

    toggleIncludeAttachments($event) {
        if (!$event.target.checked) {
            this.confirmationModalRef.openConfirmationPopup({
                headerTitle: '',
                title: `By unticking this all attachment titles will be cleared?`,
                confirmLabel: 'Confirm',
                onConfirm: () => {
                    this.permitTemplate.include_mandatory_attachments = false;
                    this.mandatory_attachments_title = [''];
                }
            });
        } else if ($event.target.checked) {
            this.permitTemplate.include_mandatory_attachments = true;
            this.mandatory_attachments_title = [''];
            return;
        }
        $event.preventDefault();
        return;
    }

    selectValidFor() {
        this.isValidForSelected = !!(this.expireDay || this.expireHour);
    }

    toggleIncludeRegister($event) {
        if (!$event.target.checked) {
            this.permitTemplate.take_register_when = null;
            this.permitTemplate.register_config = {
                signature: 'optional',
                user_list: 'on_site_users'
            }
        }
    }

    toggleUserListSelection($event) {
        this.permitTemplate.register_config.user_list = ($event.target.checked) ? 'inducted_users' : 'on_site_users';
    }

    getUniqueId(name) {
        return name.toLowerCase().replace(/ /g,"_");
    }

    hasCloseoutSignatures(closeoutSignatures) {
        return (((closeoutSignatures || []).findIndex(sign => sign.is_closeout)) >= 0);
    }
}
