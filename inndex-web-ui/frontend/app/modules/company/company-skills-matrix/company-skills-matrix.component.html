<div class="row">
    <h6 class="col-12 mt-2">Job roles configured: {{ totalCount }}</h6>
    <div class="d-md-flex flex-md-row-reverse justify-content-between col-12" *ngIf="hasMatrixJobRoles">
        <div class="mt-2 mt-md-0 d-md-flex justify-content-end flex-fill">
            <div class="logo" style="max-width: 125px; min-height: 30px;">
                <img [src]="employer.logo_file_url" width="100%;"/>
            </div>
        </div>
        <div class="medium-font d-flex info-text">
            <span class="material-symbols-outlined align-middle align-self-center">info </span>
            <div class="ml-2">
                <ng-container *ngFor="let line of pageIntroLines; let i = index;">
                    <br *ngIf="i>0" />{{ line }}
                </ng-container>
            </div>
        </div>
    </div>
    <div class="d-flex w-100 px-3 pt-1 pb-3 pb-md-0 flex-column flex-md-row justify-content-end" [ngClass]="{'justify-content-between pt-3': hasMatrixJobRoles}">
        <search-with-filters *ngIf="hasMatrixJobRoles" searchWidth="col-md-12" class="" (searchEmitter)="searchFilter($event)"></search-with-filters>

        <div class="mt-2 mt-md-0 d-flex">
            <div class="mr-md-3 d-md-flex align-items-center" style="height: 30px;">
                <label class="justify-content-between medium-font cursor-pointer font-weight-bold mr-2 mb-0" for="skills_matrix_state">
                    <span>Competency Matrix Activated</span>
                </label>
                <div class="float-right custom-control custom-switch d-inline-block" (click)="onFeatureToggle($event)">
                    <input type="checkbox" [ngModel]="skills_matrix"
                           [checked]="skills_matrix" class="custom-control-input"
                           id="skills_matrix_state" name="skills_matrix_state">
                    <label class="custom-control-label medium-font"></label>
                </div>
            </div>
            <action-button
                [actionList]="topButtonGroup"
                (selectedActionEmmiter)="topBtnClicked($event)"
                [hideNewFeatureBtn]="true">
            </action-button>
        </div>
    </div>

    <div class="col-12">
        <ngx-skeleton-loader *ngIf="viewProcessing" count="8" [theme]="{ 'border-radius': '0', height: '50px' }">
        </ngx-skeleton-loader>
    </div>
    <ng-container *ngIf="!viewProcessing">
        <div class="col-12">
            <div class="table-responsive-sm ngx-datatable-custom" *ngIf="hasMatrixJobRoles" >
                <ngx-datatable class="bootstrap table-sm"
                               #ruleSetTable
                               [rows]="rulesPage.records"
                               [columns]="[
                                    {name:'Job Role', prop: 'job_role_name', sortable: true, headerClass: 'py-1 font-weight-bold', cellClass: 'py-1 table-cell-text'},
                                    {name:'Requirements', prop: 'child', sortable: false, headerClass: 'py-1 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: rCount},
                                    {name:'Last Updated', prop: 'id', sortable: true, headerClass: 'py-1 font-weight-bold', cellClass: 'py-1 table-cell-text', cellTemplate: updatedAt},
                                    {name:'Actions', prop: 'id', sortable: false, headerClass: 'py-1 font-weight-bold', cellClass: 'py-1 table-cell-text action-column1', maxWidth: 140, cellTemplate: actionColumn}
                                ]"
                               [columnMode]="'force'"
                               [footerHeight]="36"
                               [rowHeight]="'auto'"
                               [externalPaging]="true"
                               [externalSorting]="true"
                               [count]="rulesPage.totalCount"
                               [offset]="rulesPage.pageNumber"
                               [limit]="rulesPage.pageSize"
                               [sortType]="'single'"
                               (sort)="sortCallback($event)"
                               (page)="pageCallback($event)"
                >
                    <ng-template #rCount let-row="row" let-value="value">
                        <ng-container *ngIf="(value || []).length > 1"> {{ value.length }} </ng-container>
                        <ng-container *ngIf="(value || []).length === 1"> {{ translateRequirementChild(value[0]) }} </ng-container>
                        <ng-container *ngIf="(value || []).length === 0"> N/A </ng-container>
                    </ng-template>
                    <ng-template #updatedAt let-row="row" let-value="value">
                        {{ viewDate(+row.updatedAt) }}
                    </ng-template>
                    <ng-template #actionColumn let-row="row" let-value="value">
                        <div style="line-height: 36px;">
                            <button-group
                                    topLevelBtnClasses="btn-action p-1"
                                    [hasLabel]="false"
                                    [buttons]="rowButtonGroup"
                                    (onActionClick)="rowBtnClicked($event, row)"
                            ></button-group>
                            <!--<button class="btn btn-sm align-middle btn-action mr-2 p-0" (click)="viewRule(row)">
                                <span class="material-symbols-outlined x-large-font align-middle p-1" title="Info">
                                    info
                                </span>
                            </button>
                            <button class="btn btn-sm align-middle btn-action mr-2 p-0" (click)="openAddModal(row)">
                                <span class="material-symbols-outlined x-large-font align-middle p-1" title="Edit">
                                    edit_note
                                </span>
                            </button>
                            <button class="btn btn-sm btn-outline-default align-middle btn-action mr-2 p-0" (click)="removeRuleById(row.id, row.job_role_name)">
                                <span class="material-symbols-outlined x-large-font align-middle p-1" title="Delete">
                                    delete
                                </span>
                            </button>-->
                        </div>
                    </ng-template>
                </ngx-datatable>
            </div>

            <placeholder-page
                *ngIf="!hasMatrixJobRoles"
                [iconUrl]="pagePlaceholderIcon"
                [title]="'No job roles added'"
                [description]="[pageIntroLines.join(' ')]"
                [actionTitle]="'Add new job role'"
                (actionClicked)="openAddModal()"
            ></placeholder-page>
        </div>
    </ng-container>
    <block-loader [showBackdrop]="true" alwaysInCenter="true" [show]="modalProcessing"></block-loader>
</div>

<i-modal
    [title]="(create_request?._id) ? 'Add new job role requirement': 'Edit job role requirement'"
    rightPrimaryBtnTxt="Save"
    (onCancel)="modalInfo.ready = false;"
    (onClickRightPB)="saveInfo($event)"
    [rightPrimaryBtnDisabled]="addForm.invalid"
    #addModalHtml>
    <form #addForm="ngForm" class="" novalidate>
        <div *ngIf="modalInfo.ready" class="mx-2">
            <div class="form-group">
                <ng-select [items]="allowedJobRoles"
                           bindLabel="name" class="v-scroll-dropdown-list" appendTo="body"
                           bindValue="id" required
                           [virtualScroll]="true"
                           placeholder="Select Job Role"
                           name="job_role" #job_role="ngModel"
                           [ngModel]="(create_request.job_role_ref && create_request.job_role_ref.id) ? create_request.job_role_ref.id : create_request.job_role_ref"
                           (change)="create_request.job_role_ref = $event ? $event.id : null">
                </ng-select>
            </div>
            <div class="form-group">
                <div class="mb-1 font-weight-bold">Competency Requirements</div>
                <div class="custom-control custom-checkbox checkbox-blue mb-3">
                    <input type="checkbox" class="custom-control-input" id="optional_state"
                           name="optional_state"
                           [ngModel]="create_request._competency_optional"
                           (change)="toggleRequirements($event)"
                    />
                    <label class="custom-control-label" for="optional_state">Competencies not required for this job role</label>
                </div>
                <ng-container *ngIf="!create_request._competency_optional">
                    <ng-container *ngFor="let rule of (create_request.child || []); trackBy : trackByRowIndex; let i = index;">
                        <skills-matrix-ruleset
                            class="rule-row"
                            [key]="'c'+i"
                            [initialRule]="rule"
                            [allCompetencies]="tempCompetencies"
                            (competencyUsed)="refreshMetaCompetencies($event, i)"
                            (changed)="ruleSetChanged($event, i)"
                        ></skills-matrix-ruleset>
                    </ng-container>
                    <button class="btn btn-sm btn-outline-brandeis-blue mt-2" (click)="addRequirementRow()">+ Additional requirement</button>
                    <input class="d-none" type="text" name="count-valid" required="required" [ngModel]="(create_request.child || []).length ? 'ok' : undefined" />
                </ng-container>
            </div>
        </div>
    </form>
</i-modal>
<i-modal
    [title]="'Job Role: ' + viewModalInfo.title"
    (onCancel)="viewModalInfo.ready = false;"
    [showFooter]="false"
    #vModalHtml>
    <div *ngIf="viewModalInfo.ready" class="mx-2">
        <ng-container *ngIf="viewModalInfo.record && viewModalInfo.record.child && (viewModalInfo.record.child.length === 0); else elseHasRecords">
            This job role does not require any competencies to be allowed on site
        </ng-container>
        <ng-template #elseHasRecords>
            <p>The {{ viewModalInfo.record?.job_role_ref?.name}} job role will require the user to add the following competencies/certifications when carrying out an induction onto any {{employer.name}} <span class="text-lowercase">Projects</span></p>
            <table class="table table-sm border small">
                <tbody>
                <ng-container *ngFor="let children of viewModalInfo.record.child">
                    <tr>
                        <td style="font-weight: 500;" class="border pl-2 modal-header-background">One of following</td>
                        <td class="pl-2">
                            <ng-container *ngFor="let e of children.entries; let i = index;">
                                <div><span class="badge badge-bg">{{ e?.name }}</span></div>
                            </ng-container>
                        </td>
                    </tr>
                </ng-container>
                </tbody>
            </table>
        </ng-template>
    </div>
</i-modal>
<i-modal
        title="Exemption List"
        rightPrimaryBtnTxt="Save"
        (onClickRightPB)="saveExemptionList($event)"
        (onCancel)="manageExemptionModal.ready = false;"
        #manageExemptionModalRef>
    <form #exemptionForm="ngForm" class="" novalidate>
        <div *ngIf="manageExemptionModal.ready" class="">
            <div>
                <div class="fw-500 mb-1">Add user to mandatory exemption list</div>
                <div class="mb-3">
                    Enter a user's account email address below to add to the exemption list.
                    Users on the exemption list will not be required to upload any mandatory competencies.
                    Please note: The user must have an innDex account to enable them to be added onto the exemption list.
                </div>

                <div class="">
                    <ng-select
                            [items]="records$ | async"
                            [typeahead]="userInput$"
                            [loading]="recordsLoading"
                            [trackByFn]="trackByFn"
                            (change)="onSelection($event, selectDropdown)"
                            (close)="onSelectionClose()"
                            [(ngModel)]="manageExemptionModal.selection"
                            name="exempted_users"
                            bindLabel="name"
                            [minTermLength]="5"
                            class="dropdown-list large-select"
                            appendTo="body"
                            #selectDropdown
                            placeholder="Enter email address to search"
                            typeToSearchText="Enter email address...">
                        <ng-template ng-option-tmp let-item="item" let-item$="item$" let-index="index" let-search="searchTerm">
                            <div class="d-flex justify-content-between">
                                <div>{{ item.name }}</div>
                                <small *ngIf="item.id">(ID: {{ item.id }})</small>
                            </div>
                        </ng-template>
                    </ng-select>
                    <ngb-accordion class="i-accordion compact-list" *ngIf="manageExemptionModal.exempted_users.length > 0">
                        <ngb-panel cardClass="mt-3">
                            <ng-template ngbPanelHeader let-opened="opened">
                                <button ngbPanelToggle class="btn p-0">
                                    <div class="w-100 d-flex justify-content-between">
                                        <div>
                                            <div class="m-0 fw-500">
                                                Exemption List
                                                <span [ngPlural]="manageExemptionModal.exempted_users.length" class="fw-400">
                                                    (<ng-template ngPluralCase="=1">1 User</ng-template>
                                                    <ng-template ngPluralCase="other">{{ manageExemptionModal.exempted_users.length }} Users</ng-template>)
                                                </span>
                                            </div>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <span class="material-symbols-outlined">{{ opened ? 'keyboard_arrow_up' : 'keyboard_arrow_down' }}</span>
                                        </div>
                                    </div>
                                </button>
                            </ng-template>
                            <ng-template ngbPanelContent>
                                <div class="text-search list-search horizontal-center">
                                    <span class="material-symbols-outlined large-font text-muted">search</span>
                                    <input class="w-100 ngx-search d-inline-flex"
                                           #textSearch
                                           name="search-list"
                                           type="search"
                                           autocomplete="off"
                                           placeholder="Search"
                                           (input)='filterUsersList($event)'
                                    />
                                </div>
                                <ng-container *ngFor="let row of manageExemptionModal.filtered_list; let isFirst = first; let isLast = last">
                                    <div class="d-flex justify-content-between align-items-center" [class.border-top]="isFirst" [class.border-bottom]="!isLast">
                                        <div> {{ row.name }}
                                            <span class="d-block medium-font mt-n1 text-muted">{{row.email}}</span>
                                        </div>
                                        <span class="material-symbols-outlined text-danger cursor-pointer" (click)="onRemoveItem(row)">delete</span>
                                    </div>
                                </ng-container>
                            </ng-template>
                        </ngb-panel>
                    </ngb-accordion>
                </div>
            </div>
        </div>
    </form>
</i-modal>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
