<div class="d-flex" id="wrapper">
    <company-side-nav [employer]="employer" [companyResolverResponse]="companyResolverResponse"></company-side-nav>
    <div class="row mx-0 w-100 detail-page-header-margin" [ngClass]="{'ml-fix': !is_mobile_nav}">
            <project-header [isCompanyHeader]="true" [parentCompany]="employer" class="col-sm-12 mt-3 nav-tabs"></project-header> 
            <div class="col-sm-12 my-3 outer-border">
                <div class="col-sm-12 my-3 outer-border-radius">
            <div>
                <div class="d-flex flex-column flex-md-row flex-wrap justify-content-between">
                    <div class="search-width">
                        <search-with-filters [loading]="employeesLoading" (searchEmitter)="searchFunction($event)"></search-with-filters>
                    </div>
                    <div class="float-md-right d-sm-block d-flex" style="z-index: 999">
                        <button class="btn flex-grow-1 other-action-btn btn-sm btn-brandeis-blue justify-content-center d-flex align-items-center pointer-cursor" (click)="filterRecordsPopup()" id="dropdownDlReport1">
                            <span class="medium-font m-font-size material-symbols-outlined mr-2">download</span>
                            <div class="medium-font m-font-size">Download Training Records</div>
                        </button>
                    </div>
                </div>
            <div class="col-sm-12" *ngIf="employeesLoading">
                <ngx-skeleton-loader count="8" [theme]="{ 'border-radius': '0', height: '30px', width: '100%' }"></ngx-skeleton-loader>
            </div>
            <div class="col-sm-12 table-responsive-sm" *ngIf="!employeesLoading">
                <ngx-datatable
                    class="table table-hover table-sm bootstrap"
                    [rows]="employees_details"
                    [columnMode]="'force'"
                    [limit]="50"
                    [footerHeight]="36"
                    [sorts]="[{prop: 'user_ref.name', dir: 'asc'}]"
                    [rowHeight]="'auto'">

                    <ngx-datatable-column headerClass="font-weight-bold" cellClass="pl-2 pr-2 pt-1 pb-1" [sortable]="false">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Name
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            {{row.user_ref.name}} (ID: {{ row?.user_ref?.id || row.user_ref }})
                        </ng-template>
                    </ngx-datatable-column>

                    <ngx-datatable-column headerClass="font-weight-bold" cellClass="pl-2 pr-2 pt-1 pb-1" [sortable]="false">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Job Role
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            {{row.job_role}}
                        </ng-template>
                    </ngx-datatable-column>
                    <ngx-datatable-column headerClass="font-weight-bold" cellClass="pl-2 pr-2 pt-1 pb-1" prop="user_ref.createdAt" [sortable]="false">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Account Created
                        </ng-template>
                        <ng-template let-row="row" let-value="value" ngx-datatable-cell-template>
                            {{ value ? displayDate(+value, AppConstant.displayDateFormat) : '' }}
                        </ng-template>
                    </ngx-datatable-column>

                    <ngx-datatable-column headerClass="font-weight-bold" cellClass="pl-2 pr-2 pt-1 pb-1" [sortable]="false">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Last Active
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <ng-container *ngIf="row.user_ref">
                                {{ row.user_ref.last_active_on ? unix(row.user_ref.last_active_on).format(AppConstant.displayDateFormat) : '-' }}
                            </ng-container>
                        </ng-template>
                    </ngx-datatable-column>

                    <ngx-datatable-column headerClass="font-weight-bold action-column"
                                          cellClass="action-column pl-2 pr-2 pt-1 pb-1 employerGridActions" [sortable]="false">
                        <ng-template let-column="column" ngx-datatable-header-template>
                            Action
                        </ng-template>
                        <ng-template let-row="row" ngx-datatable-cell-template>
                            <button title="View Employee Information"
                                    class="btn btn-sm btn-outline-primary mr-1"
                                    (click)="viewEmployeeInformation(row)">
                                <i class="fa fa-search"></i>
                            </button>
                            <button title="Download PDF"
                                    class="btn btn-sm btn-outline-primary mr-1"
                                    (click)="downloadCompanyEmployeeInfo(row.user_ref)">
                                <i class="fa fa-download"></i>
                            </button>
                            <button title="Delete Employee"
                            class="delete-employee btn btn-sm btn-outline-primary p-0"
                            (click)="openModal(row)">
                                <span class="material-symbols-outlined">
                                    delete
                                </span>
                            </button>
                        </ng-template>
                    </ngx-datatable-column>
                </ngx-datatable>
            </div>
        </div>
        <block-loader [show]="(downloadTimeSheetReportLoading)" [showBackdrop]="true"></block-loader>
        </div>
        </div>
        </div>
    </div>

<i-modal #viewEmployeeInformationHtml title="Employee Information" size="xl" [showCancel]="false" rightPrimaryBtnTxt="OK" (onClickRightPB)="closeEmployeeInfoModal($event)" (onCancel)="closeEmployeeInfoModal()">
    <ng-container *ngIf="showEmployeeInfoModal">
        <div class="row mb-3">
            <div class="col-md-3">
                <img [src]="employee_row?.user_ref.profile_pic" class="avatar img-circle img-thumbnail" alt="avatar">
            </div>
            <div class="col-md-9">
                <table class="table table-sm table-bordered pb-2">
                    <tbody>
                        <tr>
                            <td class="tr-bg-dark-color w-25"> <strong>Name:</strong> </td>
                            <td> {{ employee_row?.user_ref.name }} </td>
                        </tr>
                        <tr>
                            <td class="tr-bg-dark-color w-25"> <strong>Job Role:</strong> </td>
                            <td> {{ employee_row?.job_role }} </td>
                        </tr>
                        <tr>
                            <td class="tr-bg-dark-color w-25"> <strong>Current Project:</strong> </td>
                            <td> {{ currentProjectInfo?.name }} </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="row">
            <nav class="col-md-12 text-center">
                <div ngbNav (navChange)="onNavChange($event)" #infoNav="ngbNav" class="nav nav-tabs col-md-8 d-flex justify-content-center pl-3 border-bottom-0" id="nav-tab" role="tablist" style="margin: 0 auto;">
                    <ng-container [ngbNavItem]="'nav-personal-details'">
                        <a class="ml-3 nav-item nav-link" title="Personal Details" ngbNavLink><i class="fas fa-user mr-1"></i></a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <div class="text-left pb-2 card-text col-md-6 float-left">
                                        <h4 class="card-title"><i class="fas fa-user mr-1"></i>&nbsp;Personal Details</h4>
                                        <table class="table table-sm table-bordered pb-0">
                                            <tbody>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>Title:</strong> </td>
                                                    <td class="text-break"> {{ employee_row?.user_ref.title }} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>First Name:</strong> </td>
                                                    <td class="text-break"> {{ employee_row?.user_ref.first_name }} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>Middle Name:</strong> </td>
                                                    <td class="text-break"> {{ employee_row?.user_ref.middle_name }} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>Last Name:</strong> </td>
                                                    <td class="text-break"> {{ employee_row?.user_ref.last_name }} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>Email:</strong> </td>
                                                    <td class="text-break"> {{ employee_row?.user_ref.email }} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>Date of Birth:</strong> </td>
                                                    <td class="text-break"> {{ displayDate(employee_row?.user_ref.dob, AppConstant.defaultDateFormat) }} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>Gender:</strong> </td>
                                                    <td class="text-break"> {{ employee_row?.user_ref.gender }} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong><span i18n="@@nin">National Insurance Number</span>:</strong> </td>
                                                    <td class="text-break"> {{ employee_row?.user_ref.nin }} </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>

                                    <div class="text-left pb-2 card-text col-md-6 float-left">
                                        <h4 class="card-title"><i class="fa fa-phone mr-1"></i>&nbsp;Contact Details</h4>
                                        <table class="table table-sm table-bordered pb-0">
                                            <tbody>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong><span i18n="@@pc">Post Code</span>:</strong> </td>
                                                    <td class="text-break"> {{ contactDetails?.post_code }} </td>
                                                </tr>
                                                <tr *ngIf="contactDetails.district_name">
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>District:</strong> </td>
                                                    <td class="text-break"> {{ contactDetails?.district_name }} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>City:</strong> </td>
                                                    <td class="text-break"> {{ contactDetails?.city }} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>Home Phone No.:</strong> </td>
                                                    <td class="text-break"> {{ contactDetails?.home_number?.code ? '(+' + contactDetails.home_number.code + ')' : '' }} {{contactDetails?.home_number?.number}} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>Mobile No.:</strong> </td>
                                                    <td class="text-break"> {{ contactDetails?.home_number?.code ? '(+' + contactDetails.mobile_number.code + ')' : '' }} {{contactDetails?.mobile_number?.number}} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>Emergency Contact:</strong> </td>
                                                    <td class="text-break"> {{ contactDetails?.emergency_contact }} </td>
                                                </tr>
                                                <tr>
                                                    <td width="40%" class="tr-bg-dark-color"> <strong>Emergency Contact No.:</strong> </td>
                                                    <td class="text-break"> {{ contactDetails?.emergency_contact_number?.code ? '(+' + contactDetails.emergency_contact_number.code + ')' : '' }} {{contactDetails?.mobile_number?.number}} </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ng-container>

                    <ng-container [ngbNavItem]="'nav-health-assessment'">
                        <a class="nav-item nav-link" (click)="getMyHealthAssessmentAnswers(employee_row?.user_ref.id)" title="Health assessment" ngbNavLink><i class="fas fa-stethoscope mr-1"></i></a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title"><i class="fas fa-stethoscope mr-1"></i>&nbsp;Health assessment</h4>
                                    <div class="text-left pb-2 card-text">
                                        <ul class="list-group" *ngFor="let category of healthAssessmentsCategoryData; trackBy : trackByRowIndex; let i = index;">
                                            <li class="list-group-item active text-center p-0">{{ category }}</li>
                                            <li class="list-group-item"  *ngFor="let statement of healthAssessmentsData[category]; trackBy : trackByRowIndex;">
                                                <div class="col-md-10 float-left"> {{ statement.question }}</div>
                                                <div class="col-md-2 float-left text-right font-weight-bold" [ngClass]="{ 'text-danger': statement.answer=='Yes', 'text-success':statement.answer=='No'}"> {{ statement.answer }}</div>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ng-container>

                    <ng-container [ngbNavItem]="'nav-medical-assessment'">
                        <a class="nav-item nav-link" (click)="getMyMedicalAssessmentAnswers(employee_row?.user_ref.id)" title="Medical assessment" ngbNavLink>
                            <svg style="enable-background:new 0 0 415 484;width: 25px;height: 25px;vertical-align: middle;fill: #007afe;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Layer_1" x="0px" y="0px" viewBox="0 0 484 484" xml:space="preserve">
                            <path d="M414.74,95.558c-21.512-21.516-50.12-33.364-80.544-33.364c-30.428,0-59.032,11.848-80.544,33.364l-9.044,9.044    l-9.044-9.044c-21.516-21.516-50.124-33.364-80.548-33.364c-30.428,0-59.032,11.848-80.544,33.364    c-44.416,44.408-44.416,116.672,0,161.084L236.12,418.29c2.344,2.344,5.412,3.516,8.484,3.516c3.072,0,6.14-1.172,8.492-3.512    L414.74,256.646c21.516-21.512,33.364-50.12,33.364-80.544C448.104,145.674,436.256,117.07,414.74,95.558z M397.768,239.674    l-153.16,153.164L91.444,239.674c-35.056-35.056-35.056-92.092,0-127.148c16.98-16.98,39.556-26.332,63.572-26.332    c24.012,0,46.592,9.352,63.576,26.332l17.528,17.528c4.688,4.688,12.28,4.688,16.968,0l17.528-17.528    c16.984-16.98,39.56-26.332,63.576-26.332s46.596,9.352,63.576,26.332C432.824,147.582,432.824,204.618,397.768,239.674z"/>
                                <path d="M56,213.806H12c-6.628,0-12,5.372-12,12s5.372,12,12,12h44c6.628,0,12-5.372,12-12S62.628,213.806,56,213.806z"/>
                                <path d="M431.992,214.194H317.144l-17.872-39.308c-1.348-2.972-4.284-4.78-7.624-4.684c-3.26,0.14-6.108,2.24-7.196,5.316    l-36.528,102.936l-44.408-122.98c-1.096-3.036-3.916-5.12-7.148-5.272c-3.456-0.16-6.236,1.66-7.612,4.584l-29.84,63.408h-98.92    c-4.42,0-8,3.584-8,8s5.264,7.612,9.684,7.612h104c3.1,0,4.236-1.4,5.556-4.204l23.788-50.548l45.452,125.86    c1.144,3.168,4.152,5.28,7.524,5.28c0.008,0,0.016,0,0.016,0c3.38-0.008,6.388-2.14,7.516-5.324l37.376-105.332l11.804,25.964    c1.296,2.86,5.832,4.304,8.968,4.304h120c4.416,0,6.312-3.196,6.312-7.612S436.408,214.194,431.992,214.194z"/>
                                <path d="M472,209.806h-36c-6.628,0-12,5.372-12,12s5.372,12,12,12h36c6.628,0,12-5.372,12-12S478.628,209.806,472,209.806z"/>
                        </svg>
                        </a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title"><svg style="enable-background:new 0 0 415 484;width: 25px;height: 25px;vertical-align: middle;fill: black;" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" id="Layer_1" x="0px" y="0px" viewBox="0 0 484 484" xml:space="preserve">
                                    <path d="M414.74,95.558c-21.512-21.516-50.12-33.364-80.544-33.364c-30.428,0-59.032,11.848-80.544,33.364l-9.044,9.044    l-9.044-9.044c-21.516-21.516-50.124-33.364-80.548-33.364c-30.428,0-59.032,11.848-80.544,33.364    c-44.416,44.408-44.416,116.672,0,161.084L236.12,418.29c2.344,2.344,5.412,3.516,8.484,3.516c3.072,0,6.14-1.172,8.492-3.512    L414.74,256.646c21.516-21.512,33.364-50.12,33.364-80.544C448.104,145.674,436.256,117.07,414.74,95.558z M397.768,239.674    l-153.16,153.164L91.444,239.674c-35.056-35.056-35.056-92.092,0-127.148c16.98-16.98,39.556-26.332,63.572-26.332    c24.012,0,46.592,9.352,63.576,26.332l17.528,17.528c4.688,4.688,12.28,4.688,16.968,0l17.528-17.528    c16.984-16.98,39.56-26.332,63.576-26.332s46.596,9.352,63.576,26.332C432.824,147.582,432.824,204.618,397.768,239.674z"/>
                                    <path d="M56,213.806H12c-6.628,0-12,5.372-12,12s5.372,12,12,12h44c6.628,0,12-5.372,12-12S62.628,213.806,56,213.806z"/>
                                    <path d="M431.992,214.194H317.144l-17.872-39.308c-1.348-2.972-4.284-4.78-7.624-4.684c-3.26,0.14-6.108,2.24-7.196,5.316    l-36.528,102.936l-44.408-122.98c-1.096-3.036-3.916-5.12-7.148-5.272c-3.456-0.16-6.236,1.66-7.612,4.584l-29.84,63.408h-98.92    c-4.42,0-8,3.584-8,8s5.264,7.612,9.684,7.612h104c3.1,0,4.236-1.4,5.556-4.204l23.788-50.548l45.452,125.86    c1.144,3.168,4.152,5.28,7.524,5.28c0.008,0,0.016,0,0.016,0c3.38-0.008,6.388-2.14,7.516-5.324l37.376-105.332l11.804,25.964    c1.296,2.86,5.832,4.304,8.968,4.304h120c4.416,0,6.312-3.196,6.312-7.612S436.408,214.194,431.992,214.194z"/>
                                    <path d="M472,209.806h-36c-6.628,0-12,5.372-12,12s5.372,12,12,12h36c6.628,0,12-5.372,12-12S478.628,209.806,472,209.806z"/>
                                </svg>&nbsp;Medical assessment</h4>
                                    <div class="text-left pb-2 card-text">
                                        <ul class="list-group">
                                            <ng-template ngFor let-statement [ngForOf]="(medicalAssessmentsData || [])" let-i="index">
                                                <li class="list-group-item">
                                                    <div class="col-md-10 float-left"> {{ statement.question }}</div>
                                                    <div class="col-md-2 float-left text-right font-weight-bold" [ngClass]="{ 'text-danger': statement.answer=='Yes', 'text-success':statement.answer=='No'}"> {{ statement.answer }}</div>
                                                    <div class="col-md-12 d-block float-left" *ngIf="statement.ans_details"><strong>Details:</strong> {{ statement.ans_details }}</div>
                                                </li>
                                            </ng-template>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ng-container>

                    <ng-container [ngbNavItem]="'nav-employment'">
                        <a class="nav-item nav-link" title="Employment" ngbNavLink><i class="fas fa-toolbox mr-1"></i></a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <div class="text-left pb-2 card-text col-md-12 float-left">
                                        <h4 class="card-title"><i class="fas fa-toolbox mr-1"></i>&nbsp;Employment</h4>
                                        <div class="text-left pb-2 card-text">
                                            <table class="table table-sm table-bordered pb-0">
                                                <tbody>
                                                    <tr>
                                                        <td class="tr-bg-dark-color w-50">
                                                            <strong>Company:</strong>
                                                        </td>
                                                        <td>
                                                            {{ employee_row?.employer }} <ng-container *ngIf="employee_row.employment_company">({{ employee_row.employment_company }})</ng-container>
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="tr-bg-dark-color w-50">
                                                            <strong>Job Role:</strong>
                                                        </td>
                                                        <td>
                                                            {{ employee_row?.job_role }}
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="tr-bg-dark-color w-50">
                                                            <strong>Type of employment:</strong>
                                                        </td>
                                                        <td>
                                                            {{ employee_row?.type_of_employment }}
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="tr-bg-dark-color w-50">
                                                            <strong>Time with employer:</strong>
                                                        </td>
                                                        <td>
                                                            {{ createEmploymentTime(employee_row?.start_date_with_employer) }}
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="tr-bg-dark-color w-50">
                                                            <strong>Do you earn above min. living wage:</strong>
                                                        </td>
                                                        <td>
                                                            {{ numberToYesNo(employee_row?.earn_mlw_e783) }}
                                                        </td>
                                                    </tr>
                                                    <tr *ngIf="employee_row?.employee_number">
                                                        <td class="tr-bg-dark-color w-50">
                                                            <strong>Employee Number:</strong>
                                                        </td>
                                                        <td class="text-break"> {{employee_row?.employee_number}} </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <table class="table-borderless w-100">
                                                <tr>
                                                    <th valign="top">Comments:</th>
                                                    <td valign="top" style="width: 79%;">
                                                        <textarea [rows]="textAreaRows" #commentTextArea [(ngModel)]="empDetailComment" placeholder="Enter comment here" (keyup)="textareaAutoGrow(commentTextArea)" class="w-100 form-control"
                                                                  style="resize: none; overflow: hidden; min-height: 45px;" [disabled]="!isEmpCommentEnable">
                                                        </textarea>
                                                    </td>
                                                    <td valign="top">
                                                        <button *ngIf="isEmpCommentEnable" type="button" class="btn btn-primary" (click)="saveComment()">Save</button>
                                                        <button *ngIf="!isEmpCommentEnable && employee_row?.comment" type="button" class="btn btn-primary" (click)="enableEmpCommentEdit()">Edit</button>
                                                    </td>
                                                </tr>
                                            </table>
                                            <!--<p class="mb-1"><strong>Working Arrangement:</strong> {{ employee_row?.working_arrangement }}</p>
                                            <p class="mb-1"><strong>Working Pattern:</strong> {{ employee_row?.working_pattern }}</p>-->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ng-container>

                    <ng-container [ngbNavItem]="'nav-competencies'">
                        <a class="nav-item nav-link" (click)="getMyCompetencies(employee_row?.user_ref.id)" title="Competencies/Certs" ngbNavLink><i class="fas fa-id-card mr-1"></i></a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title"><i class="fas fa-id-card mr-1"></i>&nbsp;Competencies & Certifications
                                        <button class="btn btn-sm btn-link mr-1 pr-1 pl-1 float-right"
                                                (click)="openMoreUploadPopup(employee_row)"
                                                title="Upload Competency/Certification">
                                            <i class="fa fa-edit"></i> Upload Competency/Certification</button>
                                    </h4>
                                    <div class="text-left pb-2 card-text">
                                        <table class="table table-reflow">
                                            <thead>
                                            <tr>
                                                <th>Name</th>
                                                <th>Number</th>
                                                <th>Expiry Date</th>
                                                <th>Actions</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr *ngFor="let item of competencies">
                                                <td>{{ item?.name }} <i *ngIf="item.children && item.children.length" class="badge badge-pill badge-primary small">+{{item.children.length}}</i></td>
                                                <td>{{ item?.doc_number }}</td>
                                                <td>{{ dayjs(+item?.expiry_date).format(AppConstant.defaultDateFormat) }}</td>
                                                <td ngbDropdown>
                                                    <a class="btn btn-sm btn-outline-primary mr-1" ngbDropdownToggle id="downloadF"><i class="fa fa-search"></i>
                                                    </a>
                                                    <ul class="dropdown-menu" style="top:37px;font-size: 0.75rem;" ngbDropdownMenu aria-labelledby="downloadF">
                                                        <li *ngFor="let f of item.user_files; let count=index" class="dropdown-item dropdown-item-text" (click)="downloadImage(f.file_url, f.name)">
                                                            Download {{ ((count == 0) ? 'Front' : ((count == 1) ? 'Back' : 'Additional Image ' + (count - 1))) }}
                                                        </li>
                                                    </ul>
                                                    <span *ngIf="item?.is_verified" class="mr-1 btn btn-outline-success btn-sm" title="CITB Verified">
                                                            Verified <i class="fas fa-check-square"></i>
                                                    </span>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ng-container>

                    <ng-container [ngbNavItem]="'nav-time-management'">
                        <a class="nav-item nav-link" (click)="getUserTimeDetails(employee_row?.user_ref, employee_row?.job_role)" title="Time Management" ngbNavLink><i class="far fa-clock mr-1"></i></a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title"><i class="far fa-clock mr-1"></i>&nbsp;Time Management</h4>
                                    <div class="text-left pb-2 card-text row">
                                        <div class="col-sm-4 offset-md-4 my-2 form-group">
                                            <ng-select [items]="userProjects"
                                                       bindLabel="name"
                                                       bindValue="id"
                                                       placeholder="Select a Project"
                                                       name="type_of_works"
                                                       [(ngModel)]="userProjectId"
                                                       (change)="filterTimeLogs()"
                                                       [clearable]="true"
                                            >
                                            </ng-select>

                                        </div>

                                        <div class="col-sm-4 my-2">

                                            <button class="btn btn-sm btn-brandeis-blue float-md-right download-records" (click)="downloadTimeRecordsPopup()">Download Records</button>

                                        </div>

                                    </div>
                                    <div class="col-sm-12 table-responsive-sm">
                                        <ngx-datatable
                                                class="table table-hover table-sm bootstrap"
                                                [rows]="timeDetailsFiltered"
                                                [columnMode]="'force'"
                                                [limit]="50"
                                                [footerHeight]="36"
                                                [sorts]="[{prop: 'user_ref.name', dir: 'asc'}]"
                                                [rowHeight]="'auto'">

                                            <ngx-datatable-column headerClass="font-weight-bold" cellClass="pl-2 pr-2 pt-1 pb-1">
                                                <ng-template let-column="column" ngx-datatable-header-template>
                                                    Date
                                                </ng-template>
                                                <ng-template let-row="row" ngx-datatable-cell-template>
                                                    {{ dayjs(row.day_of_yr).format(AppConstant.displayDateFormat)}}
                                                </ng-template>
                                            </ngx-datatable-column>

                                            <ngx-datatable-column headerClass="font-weight-bold" cellClass="pl-2 pr-2 pt-1 pb-1">
                                                <ng-template let-column="column" ngx-datatable-header-template>
                                                    Project
                                                </ng-template>
                                                <ng-template let-row="row" ngx-datatable-cell-template>
                                                    {{getProjectName(row.project_id)}}
                                                </ng-template>
                                            </ngx-datatable-column>

                                            <ngx-datatable-column headerClass="font-weight-bold" cellClass="pl-2 pr-2 pt-1 pb-1">
                                                <ng-template let-column="column" ngx-datatable-header-template>
                                                    Job Title
                                                </ng-template>
                                                <ng-template let-row="row" ngx-datatable-cell-template>

                                                    {{jobRole}}
                                                </ng-template>
                                            </ngx-datatable-column>
                                            <ngx-datatable-column headerClass="font-weight-bold" cellClass="pl-2 pr-2 pt-1 pb-1">
                                                <ng-template let-column="column" ngx-datatable-header-template>
                                                    In Time
                                                </ng-template>
                                                <ng-template let-row="row" ngx-datatable-cell-template>

                                                    {{ row.clock_in ? unix(+row.clock_in).format('HH:mm:ss') : '-'}}
                                                </ng-template>
                                            </ngx-datatable-column>
                                            <ngx-datatable-column headerClass="font-weight-bold" cellClass="pl-2 pr-2 pt-1 pb-1">
                                                <ng-template let-column="column" ngx-datatable-header-template>
                                                    Out Time
                                                </ng-template>
                                                <ng-template let-row="row" ngx-datatable-cell-template>
                                                    {{ row.clock_out ? unix(+row.clock_out).format('HH:mm:ss') : '-'}}
                                                </ng-template>
                                            </ngx-datatable-column>
                                            <ngx-datatable-column headerClass="font-weight-bold" cellClass="pl-2 pr-2 pt-1 pb-1">
                                                <ng-template let-column="column" ngx-datatable-header-template>
                                                    Total Time
                                                </ng-template>
                                                <ng-template let-row="row" ngx-datatable-cell-template>
                                                    {{ row.effective_time ? getTimeFromSeconds(row.effective_time).format('HH:mm:ss') : '' }}
                                                    <span *ngIf="row.adjustment" class="small"> (<ng-container *ngIf="row.adjustment > 0">+</ng-container>{{ row.adjustment }} minutes)</span>
                                                </ng-template>
                                            </ngx-datatable-column>

                                        </ngx-datatable>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ng-container>

                    <ng-container [ngbNavItem]="'nav-health-and-safety'">
                        <a class="nav-item nav-link" title="Health and Safety" ngbNavLink><i class="fas fa-hard-hat mr-1"></i></a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title"><i class="fas fa-hard-hat mr-1"></i>&nbsp;Health and Safety</h4>
                                    <div class="text-left pb-2 card-text">

                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ng-container>

                    <ng-container [ngbNavItem]="'nav-document-sign'">
                        <a class="nav-item nav-link" (click)="getSignDocuments(employee_row?.user_ref.id)" title="Documents" ngbNavLink><i class="fas fa-file-signature"></i></a>
                        <ng-template ngbNavContent>
                            <div class="card">
                                <div class="card-body">
                                    <h4 class="card-title"><i class="fas fa-file-signature mr-1"></i>&nbsp;Document Sign-off
                                        <button class="btn btn-sm btn-link mr-1 pr-1 pl-1 float-right"
                                                (click)="uploadDocumentPopup(employee_row)"
                                                title="Upload more documents">
                                            <i class="fa fa-edit"></i>Add new document</button>
                                    </h4>
                                    <div class="text-left pb-2 card-text">
                                        <table class="table table-reflow" style="table-layout: fixed;">
                                            <thead>
                                            <tr>
                                                <th>Title</th>
                                                <th>Sent</th>
                                                <th>Status</th>
                                                <th>Signed</th>
                                                <th>Actions</th>
                                            </tr>
                                            </thead>
                                            <tbody>
                                            <tr *ngFor="let item of signedDocuments">
                                                <td>{{ item?.document_title }}</td>
                                                <td>{{ dayjs(+item?.createdAt).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) }}</td>
                                                <td>
                                                    <div *ngIf="(item?.sign && item?.sign.sign) else unsigned">
                                                        <span style="color: #75c175; display: inline-block;">
                                                            <i class="fas fa-check"></i>
                                                        </span>
                                                        Signed
                                                    </div>
                                                    <ng-template #unsigned>
                                                        <button class="btn btn-sm p-0 ml-2" (click)="resendSignDocument(item)" [innerHTML]="getResendContent(item?.resend_logs)"></button>
                                                    </ng-template>
                                                </td>
                                                <td>
                                                    <div *ngIf="(item?.sign && item?.sign.signed_at)">
                                                        {{ dayjs(+item?.sign.signed_at).format(AppConstant.dateTimeFormat_DD_MM_YYYY_HH_MM_SS) }}
                                                    </div>
                                                </td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary mr-1" (click)="viewDocument(item)">
                                                        <i class="fa fa-search"></i>
                                                    </button>
                                                    <button class="btn btn-sm btn-outline-primary mr-1" (click)="downloadFile(item?.doc_file_ref?.file_url, item?.doc_file_ref?.name)">
                                                        <i class="fa fa-download"></i>
                                                    </button>
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </ng-template>
                    </ng-container>

                </div>
            </nav>
            <div class="col-md-12">
                <div class="tab-content" id="nav-tabContent">
                    <div [ngbNavOutlet]="infoNav"></div>
                </div>
            </div>
        </div>
    </ng-container>
</i-modal>

<additional-doc-uploader
        [country_code]="employer?.country_code"
        [user]="authUser$"
        [notifyUserOnUpload]="true" [disableAssociation]="true" (uploadDone)="onDocumentUpload($event)" #additionalDocUploaderComponent></additional-doc-uploader>

<i-modal #downloadTimeModalHtml title="Download Time Sheet" size="lg" [showCancel]="false" rightPrimaryBtnTxt="Download" (onClickRightPB)="downloadTimeSheet($event)" (onCancel)="closeTimesheetModal()">
    <ng-container *ngIf="showDownloadTimeSheetModal">
        <div class="form-group row">
            <label class="col-md-3 col-form-label form-control-label">Select Project:</label>
            <div class="col-md-4 align-content-center">
                <ng-select [items]="userProjects"
                           bindLabel="name"
                           bindValue="id"
                           placeholder="Select a Project"
                           name="type_of_works"
                           [(ngModel)]="userProjectId2"
                           [clearable]="false"
                >
                </ng-select>
            </div>
        </div>
        <div class="form-group row">
            <label class="col-md-3 col-form-label form-control-label">Duration:</label>
            <div class="col-md-6">
                <span *ngIf="fromDate && toDate">{{ngbMomentjsAdapter.ngbDateToDayJs(fromDate).format(AppConstant.defaultDateFormat)}} - {{ngbMomentjsAdapter.ngbDateToDayJs(toDate).format(AppConstant.defaultDateFormat)}}</span>
                <span *ngIf="!fromDate || !toDate">Select Duration</span>
                <ngb-datepicker #dp
                                (dateSelect)="onDateSelection($event)"
                                [displayMonths]="2"
                                [maxDate]="maxDate"
                                [dayTemplate]="dpTemplate" outsideDays="hidden">
                </ngb-datepicker>

                <ng-template #dpTemplate let-date let-focused="focused">
                      <span class="custom-day"
                            [class.focused]="focused"
                            [class.range]="isRange(date)"
                            [class.faded]="isHovered(date) || isInside(date)"
                            [class.disabled]="isAboveMaxRange(date)"
                            (mouseenter)="hoveredDate = date"
                            (mouseleave)="hoveredDate = null">
                        {{ date.day }}
                      </span>
                </ng-template>
            </div>
        </div>
        <div class="clearfix"></div>
    </ng-container>
</i-modal>

<i-modal #filterRecordsModal title="Download Training Records" size="md" rightPrimaryBtnTxt="Download" [rightPrimaryBtnDisabled]="!filterRecordsForm.valid || !hasSelectedUsers" (onClickRightPB)="downloadTrainingRecords($event, filterRecordsForm)" (onCancel)="closeTrainingRecords(filterRecordsForm)">
        <form novalidate #filterRecordsForm="ngForm">
            <tool-invite-modal
                [isFromEmployee]="true"
                [hasEmployerFilter]="false"
                [hasSearchByName]="true"
                [isFilteredUsersReset]="isFilteredUsersReset"
                [jobRoles]="jobRoles"
                [projectUsersData]="usersList"
                [filteredUsersData]="filteredUsers"
                (onSelectDeselectUser)="checkHasSelectedUsers($event)"
            ></tool-invite-modal>
        </form>
</i-modal>

<i-modal #signDocumentFormHtml title="Document Sign-off" size="md" rightPrimaryBtnTxt="Send" rightPrimaryBtnDisabled="" [rightPrimaryBtnDisabled]="!signDocumentForm.valid"
    (onClickRightPB)="sendSignDocument($event, signDocumentForm)" (onCancel)="closeDocumentFormModal()">
        <form novalidate #signDocumentForm="ngForm">
            <div class="form-group">
                <label>Document Title<small class="required-asterisk">*</small></label>
                <div class="input-group mb-3">
                    <input type="text" class="form-control"
                           name="document_title"
                           required="true"
                           [(ngModel)]="signedDocument.document_title">
                </div>
            </div>
            <div class="form-group" *ngIf="showSignDocumentModal">
                <label>Upload PDF<small class="required-asterisk">*</small></label>
                <input type="hidden" name="doc_file_ref" id="doc_file_ref"
                       [(ngModel)]="signedDocument.doc_file_ref" required="true"/>
                <file-uploader-v2
                    [init]="{}"
                    (uploadDone)="uploadDone($event)"
                    [allowedMimeType]="['application/pdf']"
                    [dragnDropTxt]="'Drag and drop pdf here'"
                    (deleteFileDone)="fileDeleteDone($event)"
                    [showDeleteBtn]="true"
                    [showDragnDrop]="true"
                    [showHyperlink]="true"
                    [disabled]="false"
                    [category]="'signoff-document'"
                ></file-uploader-v2>
            </div>
        </form>
</i-modal>

<i-modal #viewDocumentHtml title="Document" size="lg" [showCancel]="false" rightPrimaryBtnTxt="Done" (onClickRightPB)="closeViewDocModal($event)" (onCancel)="closeViewDocModal()" >
    <div class="pdf-viewer" style="height: 84vh">
        <ng2-pdfjs-viewer *ngIf="showViewDocModal"
            [pdfSrc]="signedDocument?.doc_file_ref?.file_url"
            [page]="1"
            [print]="false"
            [openFile]="false"
            [viewBookmark]="false"
            viewerId="viewSignedDocument"
        >
        </ng2-pdfjs-viewer>
    </div>
</i-modal>
<block-loader [show]="blockloader" [showBackdrop]="true" [alwaysInCenter]="true"></block-loader>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>
