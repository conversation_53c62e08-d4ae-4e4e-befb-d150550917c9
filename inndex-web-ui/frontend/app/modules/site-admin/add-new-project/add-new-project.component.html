<div class="container detail-page-header-margin">
    <!-- Nav -->
    <div class="stepwizard equal small col-md-offset-3">
        <div class="stepwizard-row setup-panel">
            <ng-template ngFor let-item [ngForOf]="formStages" let-i="index">
                <div class="stepwizard-step" [style.width.%]="(100/formStages.length)" (click)="goToStep(item, i)" [class.cursor-pointer]="!disableNextStep(item, i)">
                    <button type="button" [ngClass]="{
                        btn:true,
                        'btn-brandeis-blue active': !isNotThisStage(item),
                        'btn-secondary': isNotThisStage(item),
                        'btn-circle': true
                    }" [disabled]="disableNextStep(item, i)">{{ i+1 }}</button>
                    <p>{{ item }}</p>
                </div>
            </ng-template>
        </div>
        <hr />
    </div>

    <div class="row">
        <div class="col-sm-12">
            <form [ngClass]="{'form-container setup-content': true, 'd-none': isNotThisStage('Information')}" novalidate #infoForm="ngForm">
                <div class="form-group">
                    <label>
                        Project Name <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control" required #pName="ngModel" [(ngModel)]="project.name"
                        name="pName"
                        aria-describedby="pNameHelp" placeholder="Enter Project Name" />
                    <div class="alert alert-danger mt-1" [hidden]="!(pName.errors && pName.errors.required)">Project Name is required</div>
                    <div class="alert alert-danger mt-1" [hidden]="!(pName.errors && pName.errors.pattern)">Project Name should not include non-ascii characters.</div>
                </div>
                <div class="form-group">
                    <label>
                        Project/Contract Number <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control" required #project_number="ngModel" [(ngModel)]="project.project_number" name="project_number"
                           aria-describedby="pNameHelp" placeholder="Enter Project/Contract Number" />
                    <div class="alert alert-danger" [hidden]="(project_number.valid)">Project/Contract Number is required</div>

                </div>

                <div class="form-group">
                    <label>Country <small class="required-asterisk ">*</small></label>
                    <country-selector-dd [disabled]="project.id"
                            [selectedCode]="project.custom_field.country_code"
                            (selectionChanged)="onCountryCodeChange($event)"
                    ></country-selector-dd>
                </div>

                <div class="form-group" *ngIf="project.custom_field.country_code === 'GB'">
                    <label>Local Workforce Districts<i class="fas fa-info-circle ml-1" ngbTooltip="Click here to view a district breakdown" (click)="viewDistrictList()"></i></label>
                    <span class="small-font d-inline-block">innDex offers the ability to report on local workforce according to the district where a user lives. To use this feature, choose from the list below the districts you consider local.</span>
                    <ng-select [items]="meta_districts"
                               [multiple]="true"
                               bindLabel="district_name"
                               bindValue="id"
                               placeholder="Choose local districts"
                               name="uk_districts"
                               [(ngModel)]="uk_districts"
                               #ukDistricts="ngModel">
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>
                        Company <small class="required-asterisk ">*</small>
                    </label>
                    <ng-select
                            required
                            #pContractor="ngModel"
                            style="width: 100%;"
                            placeholder="Select Company"
                            [items]="employers_list"
                            bindValue="name"
                            [virtualScroll]="true" [class.v-scroll]="true"
                            bindLabel="name"
                            [disabled]="project.id"
                            name="pContractor"
                            (change)="onCompanySelect($event)"
                            [(ngModel)]="project.contractor">
                    </ng-select>
                    <div class="alert alert-danger" [hidden]="(pContractor.valid)">Company Name is required</div>
                </div>
                <div class="form-group" *ngIf="enableDivisions">
                    <label>
                        Division <small class="required-asterisk ">*</small>
                    </label>
                    <ng-select
                            required
                            #pDivision="ngModel"
                            style="width: 100%;"
                            placeholder="Select Division"
                            [items]="divisionsList"
                            bindValue="id"
                            bindLabel="name"
                            name="pDivision"
                            [(ngModel)]="project.division_ref">
                    </ng-select>
                    <div class="alert alert-danger" [hidden]="(pDivision.valid)">Division Name is required</div>
                </div>

                <div class="form-group">
                    <label>
                        Main Contact Name <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" class="form-control" required #pContactName="ngModel" [(ngModel)]="project.main_contact_name" name="pContactName"
                           placeholder="Enter Main Contact Name" />
                    <div class="alert alert-danger" [hidden]="(pContactName.valid)">Main Contact Name is required</div>
                </div>
                <div class="form-group">
                    <label>
                        Main Contact Number <small class="required-asterisk ">*</small>
                    </label>
                    <app-country-code-contact-input [contactData]="project"
                                    [name]="'main_contact_number_obj'" [isRequired]="true"
                                    [errorMessageTitle]="'Main Contact Number'" [defaultCountryCode]="project.custom_field.country_code">
                    </app-country-code-contact-input>
                </div>
                <!--<div class="form-group">
                  <label>Company name</label>
                  <input type="text" class="form-control" required #pCompanyName="ngModel" [(ngModel)]="project.company_name" name="pCompanyName"
                           placeholder="Enter Company name" />
                    <div class="alert alert-danger" [hidden]="(pCompanyName.valid)">Company name is required</div>
                </div>-->
                <div class="form-group">
                    <label>Description</label>
                    <textarea name="description" [(ngModel)]="project.description" class="form-control" placeholder="Your project description (Optional)"></textarea>
                </div>

                <div class="form-group">
                    <label>Start Date <small class="required-asterisk ">*</small></label>
                    <div class="input-group col-sm-8">
                        <input class="form-control" placeholder="dd-mm-yyyy" readonly
                               name="start_date" [(ngModel)]="project.start_date" ngbDatepicker
                               #sd="ngbDatepicker" ng-value="project.start_date" required>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary calendar" (click)="sd.toggle()" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label>End Date <small class="required-asterisk ">*</small></label>
                    <div class="input-group col-sm-8">
                        <input class="form-control" placeholder="dd-mm-yyyy" readonly
                               name="end_date" [(ngModel)]="project.end_date" ngbDatepicker
                               #ed="ngbDatepicker" ng-value="project.end_date" required>
                        <div class="input-group-append">
                            <button class="btn btn-outline-secondary calendar" (click)="ed.toggle()" type="button">
                                <i class="fa fa-calendar"></i>
                            </button>
                        </div>
                    </div>
                    <div *ngIf="!isEndDateValid" class="alert alert-danger">
                        <input type="hidden" name="end-date" id="end-date" [ngModel]="''" required />
                        End date must be later than start date.
                    </div>
                </div>

                <div class="form-group">
                    <label>Project Value <small class="required-asterisk ">*</small></label>
                    <ng-select [(ngModel)]="project.value" [items]="PROJECT_VALUE" name="value" bindValue="key" bindLabel="value" class="form-control" required ng-value="project.value">
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>Project Type <small class="required-asterisk ">*</small></label>
                    <ng-select [items]="PROJECT_TYPES" bindValue="key" bindLabel="value" [(ngModel)]="project.project_type" name="project_type" class="form-control" required ng-value="project.project_type">
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>Type of Works</label>
                    <ng-select [items]="knownTypeOfWorks"
                               [multiple]="true"
                               bindLabel="name"
                               bindValue="name"
                               placeholder="Choose Type of Works"
                               name="type_of_works"
                               [(ngModel)]="project.type_of_works"
                               #competencyName="ngModel">
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>Site Big Risks</label>
                    <ng-select #siteMainRisk="ngModel"
                               [(ngModel)]="project.site_main_risks"
                               [items]="siteMainRisks"
                               [multiple]="true"
                               name="site_main_risks"
                               placeholder="Choose Site Big Risks">
                    </ng-select>
                </div>

                <div class="form-group">
                    <label>
                        innTime Login PIN <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" pattern="\d*" minlength="6" maxlength="8" class="form-control" required #pin="ngModel" [(ngModel)]="project.pin" name="pin"
                           placeholder="innTime Login PIN" />
                    <div class="alert alert-danger" [hidden]="!(pin.errors && pin.errors.required)">Login PIN is required.</div>
                    <div class="alert alert-danger" [hidden]="!(pin.errors && pin.errors.pattern)">Login PIN must be a number</div>
                    <div class="alert alert-danger" [hidden]="!(pin.errors && pin.errors.minlength)">Login PIN minimum length must be 6 digits</div>
                </div>
                <div class="custom-control custom-checkbox p-right form-group">
                    <input type="checkbox" class="custom-control-input"
                           [(ngModel)]="hasInductionPin"
                           [checked]="hasInductionPin"
                           id="projectPinChkBx" name="hasInductionPin"
                           id="hasInductionPin">
                    <label class="custom-control-label" for="hasInductionPin">Would you like to set a pin for operative to enter in order to submit an induction? </label>
                </div>

                <div class="form-group" *ngIf="hasInductionPin">
                    <label>
                        Project Induction PIN <small class="required-asterisk ">*</small>
                    </label>
                    <input type="text" pattern="\d*" minlength="6" maxlength="8" class="form-control" required #induction_pin="ngModel" [(ngModel)]="project.induction_pin" name="induction_pin"
                           placeholder="Project pin" />
                    <div class="alert alert-danger" [hidden]="!(induction_pin.errors && induction_pin.errors.required)">Project PIN is required.</div>
                    <div class="alert alert-danger" [hidden]="!(induction_pin.errors && induction_pin.errors.pattern)">Project PIN must be a number</div>
                    <div class="alert alert-danger" [hidden]="!(induction_pin.errors && induction_pin.errors.minlength)">Project PIN minimum length must be 6 digits</div>
                </div>



                <div class="form-group">
                    <label>Client</label>
                    <input type="text" class="form-control" [(ngModel)]="project.client" name="client"
                           placeholder="Enter Client" />
                </div>

                <div class="form-group">
                    <label>Main Contract Type</label>
                    <input type="text" class="form-control" [(ngModel)]="project.main_contract_type" name="main_contract_type"
                           placeholder="Enter Main Contract Type" />
                </div>

                <div class="form-group">
                    <label>Designer</label>
                    <input type="text" class="form-control" [(ngModel)]="project.designer" name="designer"
                           placeholder="Enter Designer" />
                </div>

                <div class="form-group">
                    <label>Stakeholder</label>
                    <input type="text" class="form-control" [(ngModel)]="project.stakeholder" name="stakeholder"
                           placeholder="Enter Stakeholder" />
                </div>

                <div class="form-group" *ngIf="postcodeInput?.type === 'postcode-lookup'">
                    <label><span i18n="@@pc">Post Code</span> <small class="required-asterisk ">*</small></label>
                    <input [disabled]="project.id && project.is_active === 1" type="text" class="form-control" [(ngModel)]="project.postcode" name="postcode"  minlength="4"
                           placeholder="Enter Post Code" i18n-placeholder="@@epc" [required]="isPostcodeRequired" #pPostcode="ngModel" (focusout)="project.postcode.trim().length > 3 && validatePostcode(pPostcode)"/>
                    <div class="alert alert-danger" [hidden]="!(pPostcode.errors && pPostcode.errors.required)"><span i18n="@@pc">Post Code</span> is required</div>
                    <div class="alert alert-danger" [hidden]="!(pPostcode.errors && pPostcode.errors.valid)">Invalid <span i18n="@@pc">Post Code</span>.</div>
                </div>
                <ng-container *ngIf="postcodeInput?.type === 'address-lookup'">
                    <div class="form-group">
                        <label>
                            Project location <small class="required-asterisk ">*</small>
                        </label>
                        <div class="row">
                            <div class="col-6 form-group">
                                <label>
                                    Latitude
                                </label>
                                <input type="number" class="form-control inline-input" [disabled]="project.id && project.is_active === 1" required #pLat="ngModel" [(ngModel)]="project.custom_field.location.lat" name="pLat"
                                       placeholder="Enter Latitude" pattern="^-?(?:90(?:\.0+)?|(?:[1-8]?[0-9])(?:\.\d+)?)$"/>
                            </div>
                            <div class="col-6 form-group">
                                <label>
                                    Longitude
                                </label>
                                <input type="number" [disabled]="project.id && project.is_active === 1"class="form-control inline-input" required #pLong="ngModel" [(ngModel)]="project.custom_field.location.long" name="pLong"
                                       placeholder="Enter Longitude" pattern="^-?(?:180(?:\.0+)?|(?:1[0-7][0-9]|[1-9]?[0-9])(?:\.\d+)?)$"/>
                            </div>
                        </div>
                        <div class="alert alert-danger mt-1" *ngIf="(pLat.invalid || pLong.invalid) && (pLat.touched || pLat.dirty || pLong.touched || pLong.dirty)">
                            Project location is required
                        </div>
                        <div class="alert alert-danger mt-1" [hidden]="!pLat.errors?.pattern">Enter a valid latitude (-90 to 90).</div>
                        <div class="alert alert-danger mt-1" [hidden]="!pLong.errors?.pattern">Enter a valid longitude (-180 to 180).</div>
                    </div>
                    <div class="form-group">
                        <label>Address <small class="required-asterisk ">*</small></label>
                        <input type="text" [disabled]="project.id && project.is_active === 1" class="form-control" required #pAddress="ngModel" [(ngModel)]="project.custom_field.location.region" name="address"
                               placeholder="Enter Address" />
                        <div class="alert alert-danger mt-1" [hidden]="!(pAddress.errors && pAddress.errors.required)">Address is required</div>
                    </div>
                </ng-container>
                <div class="form-group">
                    <label>Timezone <small class="required-asterisk">*</small></label>
                    <ng-select [items]="availableTimeZones"
                        bindLabel="name"
                        bindValue="name" required
                        placeholder="Choose Timezone"
                        name="timezone"
                        [(ngModel)]="project.custom_field.timezone"
                        #projectTimeZone="ngModel">
                    </ng-select>
                    <div class="alert alert-danger" [hidden]="!(projectTimeZone.errors && projectTimeZone.errors.required)">Project timezone is required</div>
                </div>
            </form>
            <!--<form [ngClass]="{'setup-content': true, 'd-none': isNotThisStage('Select Fields')}" novalidate #selectFieldsForm="ngForm">
                <label>Select Fields you wanted to from Site user:</label>
                <input type="text" class="d-none" required #pProjFields="ngModel" [(ngModel)]="project.project_fields" name="project_fields" *ngIf="!project.project_fields || project.project_fields.length === 0"/>
                <ul class="list-group" *ngFor="let category of getKeys(user_fields);">
                    <li class="list-group-item active text-center p-0">{{ category }}</li>
                    <ng-template ngFor let-item [ngForOf]="user_fields[category]" let-i="index">
                        <li class="list-group-item">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input" (change)="changeModel($event, project.project_fields, item.id)"
                                   [checked]="project.project_fields.includes(item.id)"
                                   [id]="'customCheck' + item.id" ng-value="item.id" />
                            <label class="custom-control-label d-block" [attr.for]="'customCheck' + item.id">
                                {{ item.name_alias }}
                            </label>
                        </div>
                        </li>
                    </ng-template>
                </ul>
            </form>-->

            <!--<form [ngClass]="{'form-container setup-content': true, 'd-none': isNotThisStage('Select Template')}" novalidate #selectTemplateForm="ngForm">
                <label>Select Induction form template:</label>
                <ul class="list-group" *ngIf="formTemplates | async; let form_templates">
                    <li class="list-group-item" *ngFor="let template of form_templates; let i = index;">
                        <div class="custom-control custom-radio">
                            <input type="radio" class="custom-control-input" name="template_identifier" [id]="template.id + '-checkbox'"
                                   (change)="project.template_identifier = template.identifier"
                                   [checked]="project.template_identifier === template.identifier"
                                   [value]="template.identifier" />
                            <label class="custom-control-label" style="padding-right: 80px;" [for]="template.id + '-checkbox'"> {{ template.label }}</label>
                            <a target="_blank" [href]="'/form-template/' + template.identifier + '/preview'" class="float-right small">Preview sample</a>
                        </div>
                        <input type="hidden" name="template_identifier_val" required
                               #templateIdentifierContent="ngModel" [ngModel]="project.template_identifier" />
                    </li>

                </ul>
                <i class="small form-text text-muted">If you require a specific template / layout that is not listed above, please contact the innDex team.</i>
            </form>-->

            <form [ngClass]="{'setup-content': true, 'd-none': isNotThisStage('Induction Settings')}" novalidate id="mediaForm" #mediaForm="ngForm">
                <div class="form-group ml-n5">
                    <alternative-phrase-setting   #altPhraseCn [project]="project" [projectPortal]="true"
                        [defaultPhrase]="'Inductions'" [feature]="'induction'">
                    </alternative-phrase-setting>
                </div>
                <hr>
                <h5>Media</h5>
                <div class="custom-control custom-checkbox p-right">
                    <input type="checkbox" class="custom-control-input"
                           [(ngModel)]="project.has_media_content"
                           [checked]="project.has_media_content"
                           [disabled]="(project && project.custom_field && project.custom_field.disable && project.custom_field.disable.induction_media)"
                           name="has_media_content" (click)="toggleMediaTab($event)"
                           id="has_media_content">
                    <label class="custom-control-label small pr-5" for="has_media_content">Would you like to include a video/presentations as part of the site {{project.custom_field.induction_phrase_singlr}}? </label>
                </div>

                <ul class="list-group">
                    <ng-template ngFor let-item [ngForOf]="(project.media_resources || [])" let-i="index">
                        <li class="list-group-item border-left-0 border-right-0 pl-0" style="background: transparent;" [class.border-top-0]="(i === 0)" [class.border-bottom-0]="(i === (project.media_resources || []).length - 1)">
                            <div class="form-group row">
                                <label class="col-md-3">Target language <small class="required-asterisk">*</small></label>
                                <div class="col-md-3 pl-0">
                                    <ng-select required
                                               placeholder="Choose a language"
                                               [name]="'media_locale_' + i"
                                               [(ngModel)]="item.lang"
                                               #projectMediaLocale="ngModel">
                                        <ng-option *ngFor="let lang of locales" [value]="lang" [disabled]="isAlreadySelectedMLang(i, lang, item.type)">{{lang}}</ng-option>
                                    </ng-select>
                                    <div class="alert alert-danger" [hidden]="!(projectMediaLocale.errors && projectMediaLocale.errors.required)">Media language is required</div>
                                </div>
                                <div class="custom-control custom-switch col-md-3">
                                    <input type="checkbox" [checked]="item.type === 'url'"
                                           (click)="toggleMediaUrlMode($event, i)"
                                           class="custom-control-input" [id]="'switch' + i">
                                    <label class="custom-control-label" [for]="'switch' + i">Add Luma1 Media URL instead?</label>
                                </div>
                                <div class="custom-control custom-radio col-md-2">
                                    <input type="radio"  [name]="'d-radio'+ item.type"
                                           [checked]="item.is_default"
                                           (click)="toggleMediaDefaultState($event, item.type, i)"
                                           class="custom-control-input" [id]="'d-radio' + i">
                                    <label class="custom-control-label" [for]="'d-radio' + i">Default {{item.type}}</label>
                                </div>
                                <span class="material-symbols-outlined text-danger float-right cursor-pointer" (click)="removeMediaResource(i)" *ngIf="i || (project.media_resources || []).length > 1">
                                    delete
                                </span>
                            </div>
                            <div class="form-group mb-0 row small" *ngIf="item.type === 'file'">
                                <label class="col-sm-3 mb-0">Upload Video/PDF:</label>
                                <div class="col-sm-9 pl-0">
                                    <file-uploader-v2
                                            class="pl-0 mt-n2 d-block"
                                            [disabled]="!project.has_media_content"
                                            [init]="(item.file_ref && item.file_ref.id) ? item.file_ref : {id: item.file_ref}"
                                            [category]="'project-media'"
                                            [dragnDropTxt]="'Drag and drop video or pdf here'"
                                            [showHyperlink]="true"
                                            [maxFileSize]="(200 * 1024 * 1024)"
                                            (uploadDone)="mediaResourceUploadDone($event, i)"
                                            [allowedMimeType]="['video/mp4', 'application/pdf', 'application/x-pdf']"
                                            (deleteFileDone)="mediaResourceRemoved($event, i)"
                                            [showDeleteBtn]="true"
                                            #videoUploader></file-uploader-v2>
                                    <input type="text" class="d-none"
                                           [name]="'file_ref' + i"
                                           required
                                           #mediaFileRef="ngModel"
                                           [ngModel]="(item.file_ref && item.file_ref.id) ? true : undefined" />
                                </div>
                            </div>
                            <div class="form-group mb-0 small" *ngIf="item.type === 'url'">
                                <input type="text" class="form-control" [(ngModel)]="item.content" [name]="'media_url'+i"
                                       placeholder="Enter Media url" required #htmlMediaUrl="ngModel"
                                       pattern="^(https?):\/\/[^\s$.?#]+\.[^\s\/]+\/.*" />
                                <div class="alert alert-danger" [hidden]="!(htmlMediaUrl.errors && htmlMediaUrl.errors.required)">Media URL is required</div>
                                <div class="alert alert-danger" [hidden]="!(htmlMediaUrl.errors && htmlMediaUrl.errors.pattern)">Media URL is not valid</div>
                            </div>
                        </li>
                    </ng-template>

                    <input type="text" class="d-none"
                           name="media_resource"
                           [required]="project.has_media_content"
                           #mediaResources="ngModel"
                           [ngModel]="hasValidMediaResources() ? true : undefined" />
                    <input type="text" class="d-none"
                           name="has_media_default"
                           [required]="project.has_media_content"
                           #mediaDefault="ngModel"
                           [ngModel]="hasDefaultPerMedia() ? true : undefined" />

                    <!--
                    <ng-template ngFor let-item [ngForOf]="(project.media_file_ids || [])" let-i="index">
                        <li class="list-group-item border-0 pl-0" style="background: transparent;">
                            <div class="form-group mb-0 row small">
                                <label class="col-sm-3 pr-0 mb-0">Upload Video/PDF:</label>
                                <div class="col-sm-9">
                                    <file-uploader
                                            class="pl-0 mt-n2 d-block"
                                            [disabled]="!project.has_media_content"
                                            [init]="item"
                                            [category]="'project-media'"
                                            [dragnDropTxt]="'Drag and drop video or pdf here'"
                                            [showHyperlink]="true"
                                            [maxFileSize]="(40 * 1024 * 1024)"
                                            (uploadDone)="mediaUploadDone($event, i)"
                                            [allowedMimeType]="['video/mp4', 'video/quicktime', 'application/pdf', 'application/x-pdf']"
                                            (deleteFileDone)="fileDeleteDone($event)"
                                            [showDeleteBtn]="true"
                                            #videoUploader></file-uploader>
                                </div>
                            </div>
                        </li>
                    </ng-template>
                    -->
                </ul>
                <div class="form-group" *ngIf="project.has_media_content && (allMediaNotDefined('file') || allMediaNotDefined('url'))">
                    <label>Add further media</label>
                    <i class="fa fa-plus-circle text-primary float-right cursor-pointer" (click)="addMediaResource()"></i>
                </div>

                <!--<div class="custom-control custom-checkbox p-right" *ngIf="project.has_media_content">
                    <input type="checkbox" class="custom-control-input"
                           [(ngModel)]="project.has_html_media"
                           [checked]="project.has_html_media"
                           name="has_html_media" (click)="toggleHtmlMediaState($event)"
                           id="has_html_media">
                    <label class="custom-control-label small pr-5" for="has_html_media">Would you like to embed html as part of the site {{project.custom_field.induction_phrase_singlr}}? </label>
                </div>
                <ul class="list-group col-10 pr-0" *ngIf="project.has_media_content && project.has_html_media">
                    <li class="list-group-item border-0 pl-0">
                        <input type="text" class="form-control" [(ngModel)]="project.html_media_url" name="html_media_url"
                               placeholder="Enter Media url" required #htmlMediaUrl="ngModel"/>
                        <div class="alert alert-danger" [hidden]="!(htmlMediaUrl.errors && htmlMediaUrl.errors.required)">Media URL is required</div>
                    </li>
                </ul>
                <input type="text" class="d-none"
                       name="media_files"
                       [required]="project.has_media_content"
                       #mediaFiles="ngModel"
                       [ngModel]="hasValidMediaContent() ? true : undefined" />-->

                <div class="form-group">
                    <label>
                        Declaration <small class="required-asterisk ">*</small>
                    </label>
                    <textarea name="media_declaration_content"
                              [required]="project.has_media_content" class="form-control"
                              [disabled]="!project.has_media_content"
                              #mediaDeclarationContent="ngModel"
                              [(ngModel)]="project.media_declaration_content"
                              placeholder="Add Media Declaration Content here"
                              ng-value="project.media_declaration_content"></textarea>
                    <div class="alert alert-danger" [hidden]="(!project.has_media_content || mediaDeclarationContent.valid)">Project Media Declaration is required</div>
                </div>

                <hr>
                <div class="form-group">
                    <label>
                        <h5> {{project.custom_field.induction_phrase_singlr}} Quiz </h5>
                    </label>
                    <div class="custom-control custom-checkbox p-right">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="project._has_quiz_questions"
                               [checked]="project._has_quiz_questions"
                               name="quiz_status" (click)="toggleQStatus($event, 'quizSets', 'Quiz', 'quiz')"
                               id="quiz_status">
                        <label class="custom-control-label small pr-5" for="quiz_status"> Would you like to add a multiple choice quiz to the {{project.custom_field.induction_phrase_singlr}}? </label>
                    </div>

                    <div *ngIf="project._has_quiz_questions">
                        <ul class="list-group">
                            <li class="list-group-item" *ngFor="let quiz of quizSets; let j = index;">
                                <span class="material-symbols-outlined float-right text-danger cursor-pointer" (click)="removeElement(j, 'quiz')" *ngIf="j || quizSets.length > 1">
                                    delete
                                </span>
                                <induction-quiz-row
                                        [mediaForm]="mediaForm"
                                        [quiz]="quiz" [index]="j"
                                        [alreadySelectedLang]="getAlreadySelectedQLang(j)"
                                        (markedDefault)="toggleQDefault($event, 'quizSets')"
                                ></induction-quiz-row>
                            </li>
                            <li class="list-group-item text-center">
                                <button *ngIf="(locales.length > quizSets.length)" class="btn" (click)="addQuizRow('quiz', quizSets.length)"><i class="fa fa-plus-circle text-primary cursor-pointer"> Add new Questions Set</i></button>
                            </li>
                        </ul>
                    </div>
                </div>

                <hr>
                <div class="form-group">
                    <label>
                        <h5> Additional {{project.custom_field.induction_phrase_singlr}} Questions </h5>
                    </label>
                    <div class="custom-control custom-checkbox p-right mb-3">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="project._has_additional_questions"
                               [checked]="project._has_additional_questions"
                               name="additional_iq_status" (click)="toggleQStatus($event, 'additionalQSets', 'Additional Questions', 'question')"
                               id="additional_iq_status">
                        <label class="custom-control-label small pr-5" for="additional_iq_status"> This will add another
                            step to the {{project.custom_field.induction_phrase_singlr}} to enable the collection of any additional information during the
                            {{project.custom_field.induction_phrase_singlr}} process. </label>
                    </div>

                    <div *ngIf="project._has_additional_questions">
                        <ul class="list-group">
                            <li class="list-group-item" *ngFor="let aq of additionalQSets; let j = index;">
                                <span class="material-symbols-outlined float-right text-danger cursor-pointer" (click)="removeElement(j, 'question')" *ngIf="j || additionalQSets.length > 1">
                                    delete
                                </span>
                                <induction-question-row
                                        [mediaForm]="mediaForm" [aq]="aq" [index]="j"
                                        [alreadySelectedLang]="getAlreadySelectedIQLang(j)"
                                        (markedDefault)="toggleQDefault($event, 'additionalQSets')"
                                ></induction-question-row>
                            </li>
                            <li class="list-group-item text-center">
                                <button *ngIf="(locales.length > additionalQSets.length)" class="btn" (click)="addQuizRow('question', additionalQSets.length)"><i class="fa fa-plus-circle text-primary cursor-pointer"> Add new Questions Set</i></button>
                            </li>
                        </ul>
                    </div>
                </div>

                <hr>
                <h5 class="mb-3">Mandatory Competency/ID</h5>
                <div class="form-group">
                    <div class="custom-control custom-checkbox ml-2">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="project.is_cscs_require"
                               [checked]="project.is_cscs_require"
                               name="is_cscs_require"
                               id="is_cscs_require">
                        <label class="custom-control-label" for="is_cscs_require">Make it mandatory to submit either {{ cscs_require_msg }} card in {{project.custom_field.induction_phrase_singlr}} process</label>
                    </div>

                </div>
                <div class="form-group">
                    <div class="custom-control custom-checkbox ml-2">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="is_other_doc_required"
                               [checked]="(is_other_doc_required)"
                               (click)="toggleOtherCompetenciesRequired($event)"
                               name="is_other_doc_required"
                               id="is_other_doc_required">
                        <label class="custom-control-label" for="is_other_doc_required">Make other competencies mandatory</label>
                    </div>
                    <div *ngIf="is_other_doc_required" class="py-1 pl-2 ml-4">
                        <ng-select [items]="nonCscsCompetencies"
                                   [bindLabel]="'name'"
                                   [multiple]="true"
                                   [bindValue]="'name'" required
                                   [placeholder]="'Select multiple competencies'"
                                   name="other_doc_required" #otherDocRequiredValue="ngModel"
                                   ng-value="project.other_doc_required"
                                   [(ngModel)]="project.other_doc_required"
                        >
                        </ng-select>
                    </div>
                </div>

                <div class="form-group">
                    <div class="custom-control custom-checkbox ml-2">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="has_competency_exception_list"
                               [checked]="has_competency_exception_list"
                               (click)="toggleHasCompetencyExclusion($event)"
                               name="has_competency_exception_list"
                               [disabled]="!project.is_cscs_require && !is_other_doc_required"
                               id="has_competency_exception_list">
                        <label class="custom-control-label" for="has_competency_exception_list"> Setup mandatory competency exception list</label>
                    </div>

                    <div class="col-sm-12 pl-2 form-label small ml-4">
                        By entering an email address below, that user will be except from adding any of the mandatory competencies when carrying out their {{project.custom_field.induction_phrase_singlr}}.
                    </div>

                    <div class="col-12 pl-2 ml-4" *ngIf="has_competency_exception_list">
                        <ng-select [items]="userSearch$ | async"
                                   bindLabel="name"
                                   [multiple]="true"
                                   [hideSelected]="true"
                                   [trackByFn]="trackByUsersListFn"
                                   [minTermLength]="5"
                                   [loading]="emailSearchLoading"
                                   typeToSearchText="Please enter email address to search"
                                   placeholder="Please enter email address to search"
                                   [typeahead]="emailSearchInput$"
                                   name="exclusion_users_selection"
                                   [(ngModel)]="project.competency_exception_list">
                        </ng-select>
                    </div>
                </div>
                <div class="form-group">
                    <div class="custom-control custom-checkbox ml-2">
                        <input type="checkbox" class="custom-control-input"
                               [disabled]="!project.is_cscs_require && !is_other_doc_required"
                               (change)="(project.blacklist_user_on_expiry = project.blacklist_user_on_expiry ? null : 4)"
                               [checked]="project.blacklist_user_on_expiry"
                               name="blacklist_user_on_expiry"
                               id="blacklist_user_on_expiry">
                        <label class="custom-control-label" for="blacklist_user_on_expiry">Block user from the project if their mandatory competencies expire</label>
                    </div>
                </div>

                <hr>
                <div class="form-group">
                    <label>
                        <h5 class="mb-3">Set up supply chain</h5>
                    </label>
                    <div class="custom-control custom-checkbox p-right mb-3">
                        <input type="checkbox" class="custom-control-input" #hasSupplyChainCompanies="ngModel"
                               [(ngModel)]="project.custom_field.has_supply_chain_companies"
                               [checked]="project?.custom_field?.has_supply_chain_companies"
                               [disabled]="(company_sc_setting && company_sc_setting.active_for_all_projects)"
                               (change)="toggleSupplyChainCompanies($event)"
                               name="has_supply_chain_companies" id="has_supply_chain_companies">
                        <label class="custom-control-label small pr-5" for="has_supply_chain_companies">  Would you like to setup a list of companies that can induct onto the project?
                            If a user attempts to submit an induction under a company that isn't on the list they will be prompted to select a company from the list. </label>
                    </div>

                    <div *ngIf="project?.custom_field?.has_supply_chain_companies">
                        <div class="form-group">
                            <supply-chain-selector #supplyChainSelectorComponentRef
                                [is_project_supply_chain]="true" [company_sc_setting]="company_sc_setting"
                                [selected_supply_chain_companies]="project.custom_field.supply_chain_companies"
                                [employer]="projectContractor" (selectionChangeEmmiter)="handleSCChange($event)"
                                [project_id]="project?.id" [project_country_code]="project?.custom_field?.country_code" (scValidityChange)="onSCValidityChange($event)"
                                [project_has_supply_chain_companies]="project?.custom_field?.has_supply_chain_companies"
                                ></supply-chain-selector>
                        </div>
                    </div>
                </div>
            </form>

            <form [ngClass]="{'setup-content': true, 'd-none': isNotThisStage('Access')}" novalidate #accessForm="ngForm">
                <manage-project-site-admins #adminsComponent
                        [admins]="project.admins" [projectId]="projectId" [project]="project"
                        (adminsChanged)="onProjectAdminsChanged($event)" [isProjectAdminV1]="isProjectAdminV1"
                ></manage-project-site-admins>
                <input type="text" class="d-none"
                       name="has-valid-admins"
                       required
                       [ngModel]="adminsComponent.validationStatus ? true : undefined"/>
            </form>

            <form [ngClass]="{'form-container setup-content': true, 'd-none': isNotThisStage('Site Policies')}" novalidate #policiesForm="ngForm">

                <!--<div class="form-group">
                    <div class="custom-control custom-checkbox mb-1">
                        <input type="checkbox" class="custom-control-input"
                               [checked]="has_c_lens_policy"
                               (click)="togglePolicy($event, c_lens_policy)"
                               [name]="'has_'+c_lens_policy.key"
                               [id]="'has_'+c_lens_policy.key">
                        <label class="custom-control-label" [for]="'has_'+c_lens_policy.key">{{ c_lens_policy.policy_name }}</label>
                    </div>
                    <div class="form-group row mb-0">
                        <label class="col-md-3 col-form-label font-weight-bold form-control-label">Policy Type?</label>
                        <div class="col-md-8 form-inline pl-0">
                            <div class="custom-control custom-radio mr-2">
                                <input type="radio" class="custom-control-input" name="clp" id="clp-yes"
                                       required [value]="true" [disabled]="!has_c_lens_policy" (click)="openFileUploader(c_lens_policy, true,true)"
                                       [checked]="c_lens_policy.is_text == true"/>
                                <label class="custom-control-label" for="clp-yes"> Manually Enter</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" class="custom-control-input" name="clp" id="clp-no"
                                       required [value]="false" [disabled]="!has_c_lens_policy" (click)="openFileUploader(c_lens_policy, false, true)"
                                       [checked]="c_lens_policy.is_text == false"/>
                                <label class="custom-control-label" for="clp-no"> Add PDF</label>
                            </div>
                            <button *ngIf="!c_lens_policy.is_text && isFileSelected(c_lens_policy.policy_ref)" type="button" class="btn btn-outline-primary pt-0 pb-0 pl-1 pr-1 ml-2" (click)="openFileUploader(c_lens_policy, false, true)"><i class="fa fa-search"></i></button>
                        </div>
                    </div>
                    <div *ngIf="c_lens_policy.is_text">
                        <ckeditor *ngIf="c_lens_policy.is_text" #cLensEditor
                                [name]="'ck_'+c_lens_policy.key"
                                [(ngModel)]="c_lens_policy.policy"
                                [config]="editorConfig"
                                [readonly]="!has_c_lens_policy"
                                debounce="500"></ckeditor>

                        <input type="hidden" name="c_lens_policy_txt"
                               #cLensPolicyContent="ngModel" [ngModel]="c_lens_policy.policy"
                               [required]="!!has_c_lens_policy && c_lens_policy.is_text" />
                        <div class="alert alert-danger"
                             [hidden]="(!has_c_lens_policy || cLensPolicyContent.valid)">{{ c_lens_policy.policy_name }} content is required</div>
                    </div>

                    &lt;!&ndash; validate PDF &ndash;&gt;
                    <div *ngIf="!c_lens_policy.is_text">
                        <input type="hidden" name="c_lens_policy_file"
                               #cLensPolicyContent="ngModel" [ngModel]="isFileSelected(c_lens_policy.policy_ref)"
                               [required]="!!has_c_lens_policy" />
                        <div class="alert alert-danger"
                             [hidden]="(!has_c_lens_policy || cLensPolicyContent.valid)">{{ c_lens_policy.policy_name }} file is required</div>
                    </div>
                </div>
                <hr>-->
                <div class="form-group">
                    <div class="custom-control custom-checkbox mb-1">
                        <input type="checkbox" class="custom-control-input"
                               [checked]="has_d_and_a_policy"
                               (click)="togglePolicy($event, d_and_a_policy)"
                               [name]="'has_'+d_and_a_policy.key"
                               [id]="'has_'+d_and_a_policy.key">
                        <label class="custom-control-label" [for]="'has_'+d_and_a_policy.key">{{ d_and_a_policy.policy_name }}</label>
                    </div>
                    <div class="form-group row mb-0">
                        <label class="col-md-3 col-form-label font-weight-bold form-control-label">Policy Type?</label>
                        <div class="col-md-8 form-inline pl-0">
                            <div class="custom-control custom-radio mr-2">
                                <input type="radio" class="custom-control-input" name="dap" id="dap-yes"
                                       required [value]="true" [disabled]="!has_d_and_a_policy" (click)="openFileUploader(d_and_a_policy, true, true)"
                                       [checked]="d_and_a_policy.is_text == true"/>
                                <label class="custom-control-label" for="dap-yes"> Manually Enter</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" class="custom-control-input" name="dap" id="dap-no"
                                       required [value]="false" [disabled]="!has_d_and_a_policy" (click)="openFileUploader(d_and_a_policy, false, true)"
                                       [checked]="d_and_a_policy.is_text == false"/>
                                <label class="custom-control-label" for="dap-no"> Add PDF</label>
                            </div>
                            <button *ngIf="!d_and_a_policy.is_text && isFileSelected(d_and_a_policy.policy_ref)" type="button" class="btn btn-outline-primary pt-0 pb-0 pl-1 pr-1 ml-2" (click)="openFileUploader(d_and_a_policy, false, true)"><i class="fa fa-search"></i></button>
                        </div>
                    </div>
                    <div *ngIf="d_and_a_policy.is_text">
                        <quill-editor
                            [id]="'editor_'+d_and_a_policy.key"
                            [name]="'editor_'+d_and_a_policy.key"
                            [(ngModel)]="d_and_a_policy.policy"
                            [readOnly]="!has_d_and_a_policy"
                            [modules]="quillModule"
                        ></quill-editor>
                        <!--<ckeditor #dAndAPolicyEditor
                                  [name]="'ck_'+d_and_a_policy.key"
                                  [(ngModel)]="d_and_a_policy.policy"
                                  [config]="editorConfig"
                                  [readonly]="!has_d_and_a_policy"
                                  debounce="500"></ckeditor>-->

                        <input type="hidden" name="d_and_a_policy_txt"
                               #dAndAPolicyContent="ngModel" [ngModel]="d_and_a_policy.policy"
                               [required]="!!has_d_and_a_policy" />
                        <div class="alert alert-danger"
                             [hidden]="(!has_d_and_a_policy || dAndAPolicyContent.valid)">{{ d_and_a_policy.policy_name }} content is required</div>
                    </div>

                    <!-- validate PDF -->
                    <div *ngIf="!d_and_a_policy.is_text">
                        <input type="hidden" name="d_and_a_policy_file"
                               #dAndAPolicyContent="ngModel" [ngModel]="isFileSelected(d_and_a_policy.policy_ref)"
                               [required]="!!has_d_and_a_policy" />
                        <div class="alert alert-danger"
                             [hidden]="(!has_d_and_a_policy || dAndAPolicyContent.valid)">{{ d_and_a_policy.policy_name }} file is required</div>
                    </div>
                </div>
                <hr>
                <div class="form-group">
                    <div class="custom-control custom-checkbox mb-1">
                        <input type="checkbox" class="custom-control-input"
                               [checked]="has_working_hr_agreement"
                               (click)="togglePolicy($event, working_hr_agreement)"
                               [name]="'has_'+working_hr_agreement.key"
                               [id]="'has_'+working_hr_agreement.key">
                        <label class="custom-control-label" [for]="'has_'+working_hr_agreement.key">{{ working_hr_agreement.policy_name }}</label>
                    </div>
                    <div class="form-group row mb-0">
                        <label class="col-md-3 col-form-label font-weight-bold form-control-label">Policy Type?</label>
                        <div class="col-md-8 form-inline pl-0">
                            <div class="custom-control custom-radio mr-2">
                                <input type="radio" class="custom-control-input" name="whg" id="whg-yes"
                                       required [value]="true" [disabled]="!has_working_hr_agreement" (click)="openFileUploader(working_hr_agreement, true, true)"
                                       [checked]="working_hr_agreement.is_text == true"/>
                                <label class="custom-control-label" for="whg-yes"> Manually Enter</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" class="custom-control-input" name="whg" id="whg-no"
                                       required [value]="false" [disabled]="!has_working_hr_agreement" (click)="openFileUploader(working_hr_agreement, false, true)"
                                       [checked]="working_hr_agreement.is_text == false"/>
                                <label class="custom-control-label" for="whg-no"> Add PDF</label>
                            </div>
                            <button *ngIf="!working_hr_agreement.is_text && isFileSelected(working_hr_agreement.policy_ref)" type="button" class="btn btn-outline-primary pt-0 pb-0 pl-1 pr-1 ml-2" (click)="openFileUploader(working_hr_agreement, false, true)"><i class="fa fa-search"></i></button>
                        </div>
                    </div>
                    <div *ngIf="working_hr_agreement.is_text">
                        <quill-editor
                            [id]="'editor_'+working_hr_agreement.key"
                            [name]="'editor_'+working_hr_agreement.key"
                            [(ngModel)]="working_hr_agreement.policy"
                            [readOnly]="!has_working_hr_agreement"
                            [modules]="quillModule"
                        ></quill-editor>
                        <!--<ckeditor #wHrAgreementEditor
                                  [name]="'ck_'+working_hr_agreement.key"
                                  [(ngModel)]="working_hr_agreement.policy"
                                  [config]="editorConfig"
                                  [readonly]="!has_working_hr_agreement"
                                  debounce="500"></ckeditor>-->

                        <input type="hidden" name="working_hr_agreement_txt"
                               #wHrAgreementContent="ngModel" [ngModel]="working_hr_agreement.policy"
                               [required]="!!has_working_hr_agreement" />
                        <div class="alert alert-danger"
                             [hidden]="(!has_working_hr_agreement || wHrAgreementContent.valid)">{{ working_hr_agreement.policy_name }} content is required</div>
                    </div>

                    <!-- validate PDF -->
                    <div *ngIf="!working_hr_agreement.is_text">
                        <input type="hidden" name="working_hr_agreement_file"
                               #wHrAgreementContent="ngModel" [ngModel]="isFileSelected(working_hr_agreement.policy_ref)"
                               [required]="!!has_working_hr_agreement" />
                        <div class="alert alert-danger"
                             [hidden]="(!has_working_hr_agreement || wHrAgreementContent.valid)">{{ working_hr_agreement.policy_name }} file is required</div>
                    </div>
                </div>
                <hr>
                <div class="form-group">
                    <div *ngFor="let item of project.further_policies; trackBy : trackByRowIndex; let i = index;">
                        <div class="form-group" *ngIf="!project.further_policies[i].is_default">
                            <div class="row">
                                <div class="col-md-11 col-md-offset-1">
                                    <div class="input-group mb-3">
                                        <input type="policy_name" class="form-control"
                                               placeholder="Policy Name" #pPolicyName="ngModel"
                                               [(ngModel)]="project.further_policies[i].policy_name"
                                               ng-value="project.further_policies[i].policy_name"
                                               name="{{'policy_name_' + i}}"
                                               required autocomplete="off">
                                        <div class="alert alert-danger" [hidden]="!(pPolicyName.errors && pPolicyName.errors.required)">Policy Name is required</div>
                                    </div>
                                </div>
                                <span class="material-symbols-outlined text-danger cursor-pointer pt-10" (click)="removeFurtherPolicyRow($event, i)">
                                    delete
                                </span>
                            </div>
                            <div class="form-group row mb-0">
                                <label class="col-md-3 col-form-label font-weight-bold form-control-label">Policy Type?</label>
                                <div class="col-md-8 form-inline pl-0">
                                    <div class="custom-control custom-radio mr-2">
                                        <input type="radio" class="custom-control-input" name="{{'policy' + i}}" id="{{'policy-yes' + i}}"
                                               required [value]="true" (click)="openFileUploader(project.further_policies[i], true,false, i)"
                                               [checked]="project.further_policies[i].is_text == true"/>
                                        <label class="custom-control-label" for="{{'policy-yes' + i}}"> Manually Enter</label>
                                    </div>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" class="custom-control-input" name="{{'policy' + i}}" id="{{'policy-no' + i}}"
                                               required [value]="false" (click)="openFileUploader(project.further_policies[i], false, false, i)"
                                               [checked]="project.further_policies[i].is_text == false"/>
                                        <label class="custom-control-label" for="{{'policy-no' + i}}"> Add PDF</label>
                                    </div>
                                    <button *ngIf="!project.further_policies[i].is_text && isFileSelected(project.further_policies[i].policy_ref)" type="button" class="btn btn-outline-primary pt-0 pb-0 pl-1 pr-1 ml-2" (click)="openFileUploader(project.further_policies[i], false, false, i)"><i class="fa fa-search"></i></button>
                                </div>
                            </div>
                            <div *ngIf="project.further_policies[i].is_text" class="d-flex align-content-start mb-3">
                                <div class="flex-wrap-reverse">
                                    <quill-editor
                                        class="d-block"
                                        [id]="'editor_fp_txt_'+i"
                                        [name]="'editor_fp_txt_'+i"
                                        [(ngModel)]="project.further_policies[i].policy"
                                        [modules]="quillModule"
                                    ></quill-editor>
                                    <!--<ckeditor #pFurtherPolicyEditor
                                            name="{{'fp_txt_' + i}}"
                                            [id]="'fp_txt_' + i"
                                            [(ngModel)]="project.further_policies[i].policy"
                                            [config]="editorConfig"
                                            (contentDom)="onContentDom($event, pFurtherPolicyEditor)"
                                            debounce="500"></ckeditor>-->

                                    <input type="hidden" name="{{'fp_txt_policy' + i}}"
                                           #pFurtherPolicy="ngModel" [ngModel]="project.further_policies[i].policy" required />
                                    <div class="alert alert-danger" [hidden]="(!project.further_policies[i].policy_name || pFurtherPolicy.valid)">Policy content is required</div>
                                </div>
                            </div>
                            <!-- validate PDF -->
                            <div *ngIf="!project.further_policies[i].is_text">
                                <input type="hidden" name="{{'pf_file' + i}}"
                                       #pFurtherPolicy="ngModel" [ngModel]="isFileSelected(project.further_policies[i].policy_ref)"
                                       required/>
                                <div class="alert alert-danger" [hidden]="(!project.further_policies[i].policy_name || pFurtherPolicy.valid)">Policy file is required</div>
                            </div>
                            <hr>
                        </div>
                    </div>
                    <label>Add more policies:</label>
                    <i class="fa fa-plus-circle text-primary float-right cursor-pointer" (click)="addFurtherPolicyRow()"></i>
                </div>
            </form>

            <i-modal #policyFileUploaderRef title="Add PDF" size="lg" [showCancel]="false" (onClickRightPB)="closePolicyFileUploader($event)"
                rightPrimaryBtnTxt="Done">
                    <ul class="list-group">
                        <ng-template ngFor let-item [ngForOf]="(selectedPolicy.policy_ref || [])" let-i="index">
                            <li class="list-group-item border-0 pl-0" style="background: transparent;">
                                <div class="form-group row">
                                    <label class="col-sm-3 pr-0">Upload content:</label>
                                    <div class="col-sm-9">
                                        <file-uploader-v2
                                            class="pl-0"
                                            [init]="item"
                                            [category]="'policy-upload'"
                                            [showHyperlink]="true" [dragnDropTxt]="'Drag and drop pdf here'"
                                            (uploadDone)="policyFileUploadDone($event, i)"
                                            [allowedMimeType]="['application/pdf']"
                                            (deleteFileDone)="deletePolicyFile($event)"
                                            [showDeleteBtn]="true"
                                            [disabled]="false"
                                            #mediaUploader></file-uploader-v2>
                                    </div>
                                </div>
                            </li>
                        </ng-template>
                    </ul>
            </i-modal>

            <form [ngClass]="{'form-container setup-content': true, 'd-none': isNotThisStage('Declarations')}" novalidate #declarationForm="ngForm">
                <label>Add one or more declarations:</label>
                <i class="fa fa-plus-circle text-primary float-right cursor-pointer" (click)="addDeclarationRow()"></i>
                <div class="form-group" *ngFor="let item of project.declarations; trackBy : trackByRowIndex; let i = index;">
                    <label>Content <small class="required-asterisk">*</small></label>
                    <span class="material-symbols-outlined float-right text-danger cursor-pointer" (click)="removeDeclarationRow(i)">
                        delete
                    </span>
                    <textarea [name]="'declaration_content' + i"
                              required class="form-control" #pDeclarationContent="ngModel" [(ngModel)]="project.declarations[i].content"
                              placeholder="Add Project Declaration Content here"
                              ng-value="project.declarations[i].content"></textarea>
                    <div class="alert alert-danger" [hidden]="(pDeclarationContent.valid)">Project Declaration is required</div>
                </div>
                <div class="form-group">
                    <label>Default declaration</label>
                    <textarea class="form-control" disabled readonly>{{DEFAULT_DECLARATION}}</textarea>
                </div>

                <hr>
                <label class="mb-2">Add Daily Declarations (For mobile clock in only) <i class="fas fa-info-circle" title="Daily declarations will prompt all users to answer a series of questions before they can clock in using the mobile clock in feature. All individuals declarations will be viewable from the induction menu of the web portal."></i></label>
                <i class="fa fa-plus-circle text-primary float-right cursor-pointer" (click)="addClockInDeclarationRow()"></i>
                <ng-container *ngIf="project?.custom_field?.clock_in_declarations">
                    <ul class="list-group list-group-flush">
                        <li class="list-group-item form-group1" *ngFor="let item of project.custom_field.clock_in_declarations; trackBy : trackByRowIndex; let i = index;">
                            <label>Content <small class="required-asterisk">*</small></label>
                            <span class="material-symbols-outlined float-right text-danger cursor-pointer" (click)="removeClockInDeclarationRow(i)">
                                delete
                            </span>
                            <textarea [name]="'ci_declaration_content' + i"
                                      required class="form-control" #ciDeclarationContent="ngModel" [(ngModel)]="project.custom_field.clock_in_declarations[i].message"
                                      placeholder="Add Clock In Declaration content here"
                                      ng-value="project.custom_field.clock_in_declarations[i].message"></textarea>
                            <div class="alert alert-danger" [hidden]="(ciDeclarationContent.valid)">Clock in declaration is required</div>

                            <div class="form-group row mb-0">
                                <label class="col-md-4 col-form-label">Answer Type</label>
                                <div class="col-md-8 form-inline pl-0">
                                    <div class="custom-control custom-radio mr-2">
                                        <input type="radio" class="custom-control-input" [name]="'ci_answer' + i" [id]="'ci_answer-yn' + i"
                                               required value="y-n" [(ngModel)]="project.custom_field.clock_in_declarations[i].answer_type"
                                        />
                                        <label class="custom-control-label" [for]="'ci_answer-yn' + i"> Yes/No answer</label>
                                    </div>
                                    <div class="custom-control custom-radio mr-2">
                                        <input type="radio" class="custom-control-input" [name]="'ci_answer' + i" [id]="'ci_answer-txt' + i"
                                               required value="text" [(ngModel)]="project.custom_field.clock_in_declarations[i].answer_type"
                                        />
                                        <label class="custom-control-label" [for]="'ci_answer-txt' + i"> Text box answer</label>
                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </ng-container>
            </form>

            <!-- Delivery Management -->
            <form [ngClass]="{'form-container-addons setup-content': true, 'd-none': isNotThisStage('Project Setup')}" novalidate #addOnsForm="ngForm">
                <div class="form-group">
                    <div class="custom-control custom-checkbox ml-2">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="project.is_passport_require"
                               [checked]="project.is_passport_require"
                               name="is_passport_require"
                               id="is_passport_require">
                        <label class="custom-control-label" for="is_passport_require">Right to Work Check</label>
                    </div>

                    <div class="col-sm-12 pl-2 form-label small">
                        As part of the {{project.custom_field.induction_phrase_singlr}} would you like to make it mandatory to provide a copy of a passport / visa as a right to work check?
                    </div>
                </div>



                <hr>
                <div class="form-group" [ngbTooltip]="projectFeaturePermission.dashboards === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body"
                    [disableTooltip]="disableToolTipStatus(projectFeaturePermission.dashboards)">
                    <div class="custom-control custom-checkbox ml-2"
                         [class.disable-tool]="disableToolStatus(projectFeaturePermission.dashboards)">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="project.project_section_access.dashboards"
                               [checked]="project.project_section_access.dashboards"
                               [disabled]="disableToolStatus(projectFeaturePermission.dashboards)"
                               name="dashboards"
                               id="dashboards">
                        <label class="custom-control-label" for="dashboards">Dashboards</label>
                    </div>
                </div>
                <hr>
                <div class="form-group" [ngbTooltip]="projectFeaturePermission.time_management === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body"
                     [disableTooltip]="disableToolTipStatus(projectFeaturePermission.time_management)">
                    <div class="custom-control custom-checkbox ml-2" [class.disable-tool]="disableToolStatus(projectFeaturePermission.time_management)">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="project.project_section_access.time_management"
                               [checked]="project.project_section_access.time_management"
                               [disabled]="disableToolStatus(projectFeaturePermission.time_management)"
                               name="time_management"
                               id="time_management">
                        <label class="custom-control-label" for="time_management">Time Management</label>
                    </div>
                    <div class="ml-5 mt-1">
                        <label>If a member of the workforce forgets to either clock in or out for the day, would you like to assign them a standard number of hours for the dashbords so that the figures aren't skewed? If so, please indicate the number of hours with the scroll bar.</label>

                        <div class="btn-group btn-group-toggle" ngbRadioGroup name="timeDurationEnabled" [(ngModel)]="project.has_default_time">
                            <label ngbButtonLabel class="btn-outline-primary btn-sm" (click)="resetDefaultDuration(true)">
                                <input ngbButton type="radio" [value]="true"> Yes
                            </label>
                            <label ngbButtonLabel class="btn-outline-primary btn-sm" (click)="resetDefaultDuration(false)">
                                <input ngbButton type="radio" [value]="false"> No
                            </label>
                        </div>
                        <span *ngIf="project.default_in_duration" class="float-right"> Keep it <b>{{project.default_in_duration}}</b> hours.</span>
                        <div class="form-group my-2">
                            <input type="range"
                                   class="custom-range"
                                   min="0.25" max="24"
                                   step="0.25"
                                   [disabled]="!project.has_default_time"
                                   name="default_in_duration"
                                   #pDefaultInDuration="ngModel" [(ngModel)]="project.default_in_duration"
                            />
                        </div>

                    </div>
                </div>

                <hr>
                <div [ngbTooltip]="projectFeaturePermission.time_management === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff"  [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.timesheet)">
                    <div class="ml-2" [class.faded]="!project.project_section_access.time_management">
                        <div class="custom-control custom-checkbox">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.project_section_access.timesheet"
                                   [checked]="project.project_section_access.timesheet"
                                   [disabled]="disableToolStatus(projectFeaturePermission.time_management) || !project.project_section_access.time_management"
                                   name="timesheet"
                                   id="timesheet">
                            <label class="custom-control-label" for="timesheet">Timesheets</label>
                        </div>

                        <div class="mt-1" style="padding-left: 24px;" [class.faded]="!project.project_section_access.time_management">
                            <label class="mt-1">
                                Week Ending <small class="required-asterisk ">*</small>
                            </label>
                            <ng-select
                                    [clearable]="false"
                                    [disabled]="!project.project_section_access.timesheet"
                                    class="mt-1"
                                    [required]="project.project_section_access.timesheet && !disableToolStatus(projectFeaturePermission.time_management)"
                                    [items]="weekDays"
                                    bindValue="id"
                                    placeholder="Select week ending"
                                    name="week_ending"
                                    #weekEndingOn="ngModel"
                                    [(ngModel)]="project.custom_field.timesheet_week_end">
                            </ng-select>
                            <div class="alert alert-danger" *ngIf="project.project_section_access.timesheet" [hidden]="(weekEndingOn.valid)">Week ending info is required</div>
                        </div>

                        <div class="mt-1" style="padding-left: 24px;" [class.faded]="!project.project_section_access.time_management">
                            <label class="mt-1">
                                Currency Code <small class="required-asterisk ">*</small>
                            </label>
                            <ng-select
                                    [clearable]="false"
                                    [disabled]="!project.project_section_access.timesheet"
                                    class="mt-1"
                                    [required]="project.project_section_access.timesheet && !disableToolStatus(projectFeaturePermission.time_management)"
                                    [items]="['EUR', 'GBP', 'USD', 'AED', 'AUD', 'CAD']"
                                    placeholder="Select a Currency code"
                                    name="currency_code"
                                    #currencyCode="ngModel"
                                    [(ngModel)]="project.custom_field.currency_code">
                            </ng-select>
                            <div class="alert alert-danger" *ngIf="project.project_section_access.timesheet" [hidden]="(currencyCode.valid)">Currency code is required</div>
                        </div>
                    </div>
                </div>


                <hr class="mt-4">
                <div [ngbTooltip]="projectFeaturePermission.site_messaging === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.site_messaging)">
                    <div class="form-group" [class.disable-tool]="disableToolStatus(projectFeaturePermission.site_messaging)">
                        <div class="custom-control custom-checkbox ml-2">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.project_section_access.site_messaging"
                                   [checked]="project.project_section_access.site_messaging"
                                   [disabled]="disableToolStatus(projectFeaturePermission.site_messaging)"
                                   name="sm"
                                   id="sm">
                            <label class="custom-control-label" for="sm">Site Messaging</label>
                        </div>
                    </div>
                </div>

                <hr>
                <div [ngbTooltip]="projectFeaturePermission.good_calls === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body"
                     [disableTooltip]="disableToolTipStatus(projectFeaturePermission.good_calls)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.project_section_access.good_calls"
                                       [checked]="project.project_section_access.good_calls"
                                       [disabled]="disableToolStatus(projectFeaturePermission.good_calls)"
                                       name="gc"
                                       id="gc">
                                <label class="custom-control-label" for="gc">{{ project.custom_field.gc_phrase }}</label>
                                <div ngbDropdown [autoClose]="'outside'" class="d-inline-block mr-1" placement="bottom-right" container="body">
                                    <i class="fas fa-cog cursor-pointer ml-1 dropdown-toggle-no-caret gcSetting" ngbDropdownToggle placement="bottom-left" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="dropdownMenu2"></i>
                                    <div ngbDropdownMenu class="dropdown-menu" aria-labelledby="dropdownMenu2">
                                        <button class="dropdown-item cursor-pointer" (click)="gcAssociateEmailWithOwnerModal()" type="button">Tag Owners</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <alternative-phrase-setting  #altPhraseGc [project]="project" [projectPortal]="true"
                                                     [defaultPhrase]="'Good Calls'" [feature]="'good_calls'">
                        </alternative-phrase-setting>
                    </div>
                </div>


                <hr>
                <div [ngbTooltip]="projectFeaturePermission.observations === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.observations)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.project_section_access.observations"
                                       [checked]="project.project_section_access.observations"
                                       [disabled]="disableToolStatus(projectFeaturePermission.observations)"
                                       name="obrs"
                                       id="obrs">
                                <label class="custom-control-label" for="obrs">{{ project.custom_field.obrs_phrase }}</label>
                                <div ngbDropdown [autoClose]="'outside'" class="d-inline-block mr-1" placement="bottom-right" container="body">
                                    <i class="fas fa-cog cursor-pointer ml-1 dropdown-toggle-no-caret obrsSetting" ngbDropdownToggle placement="bottom-left" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="dropdownMenu22"></i>
                                    <div ngbDropdownMenu class="dropdown-menu" aria-labelledby="dropdownMenu22">
                                        <button class="dropdown-item cursor-pointer" (click)="obrsAssociateEmailWithOwnerModal()" type="button">Tag Owners</button>
                                        <button class="dropdown-item cursor-pointer" (click)="observationCategoriesModal()" type="button">Set Categories</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <alternative-phrase-setting  #altPhraseObrs [project]="project" [projectPortal]="true"
                                                     [defaultPhrase]="'Observations'" [feature]="'observations'">
                        </alternative-phrase-setting>
                    </div>
                </div>

                <hr>
                <div class="mb-1" [ngbTooltip]="projectFeaturePermission.close_calls === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.close_calls)">
                    <div>
                        <div class="custom-control custom-checkbox ml-2">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.project_section_access.close_calls"
                                   [checked]="project.project_section_access.close_calls"
                                   [disabled]="disableToolStatus(projectFeaturePermission.close_calls)"
                                   name="ccs"
                                   id="ccs">
                            <label class="custom-control-label" for="ccs">{{ project.custom_field.cc_phrase }}</label>
                            <div ngbDropdown [autoClose]="'outside'" class="d-inline-block mr-1" placement="bottom-right" container="body">
                                <i class="fas fa-cog cursor-pointer ml-1 dropdown-toggle-no-caret ccSetting" ngbDropdownToggle placement="bottom-left" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="dropdownMenu2"></i>
                                <div ngbDropdownMenu class="dropdown-menu" aria-labelledby="dropdownMenu2">
                                    <button class="dropdown-item cursor-pointer" (click)="ccAssociateEmailWithOwnerModal()" type="button">Tag Owners</button>
                                </div>
                            </div>
                        </div>
                        <alternative-phrase-setting #altPhraseCc [project]="project" [projectPortal]="true"
                                                    [defaultPhrase]="'Close Calls'" [feature]="'close_calls'">
                        </alternative-phrase-setting>
                    </div>
                </div>

                <hr>
                <div [ngbTooltip]="projectFeaturePermission.take_5s === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.take_5s)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.project_section_access.take_5s"
                                       [checked]="project.project_section_access.take_5s"
                                       [disabled]="disableToolStatus(projectFeaturePermission.take_5s)"
                                       name="t5ss"
                                       id="t5ss">
                                <label class="custom-control-label" for="t5ss">{{ project.custom_field.take5_phrase }}</label>
                            </div>
                        </div>
                        <alternative-phrase-setting   #altPhraseT5s [project]="project" [projectPortal]="true"
                                                      [defaultPhrase]="'Take 5s'" [feature]="'take_5s'">
                        </alternative-phrase-setting>
                        <ng-select
                                class="mt-2 ml-5 w-50"
                                [items]="briefing_signature_labels"
                                bindLabel="title"
                                [bindValue]="'id'"
                                placeholder="Select signature setting"
                                name="briefing_signatures_take_5s"
                                [(ngModel)]="project.custom_field.briefing_signatures.take_5s">
                        </ng-select>
                    </div>
                </div>

                <hr>
                <div [ngbTooltip]="projectFeaturePermission.toolbox_talks === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.toolbox_talks)">
                    <div class="form-group">
                        <div class="custom-control custom-checkbox ml-2">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.project_section_access.toolbox_talks"
                                   [checked]="project.project_section_access.toolbox_talks"
                                   [disabled]="disableToolStatus(projectFeaturePermission.toolbox_talks)"
                                   name="tts"
                                   id="tts">
                            <label class="custom-control-label" for="tts">Toolbox Talks</label>
                        </div>
                        <div class="custom-control custom-checkbox ml-5">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.custom_field.alternate_userlist_toolboxtalks"
                                   [checked]="project.custom_field.alternate_userlist_toolboxtalks"
                                   name="alternate_userlist_toolboxtalks"
                                   id="alternate_userlist_toolboxtalks">
                            <label class="custom-control-label" for="alternate_userlist_toolboxtalks">To take a register, use a list of inducted users rather than on-site users? <i class="fas fa-info-circle" [ngbTooltip]="alternativeUserListTooltip"
                                                                                                                                                                                     [openDelay]="200" [closeDelay]="500"></i></label>
                        </div>
                        <ng-select
                                class="mt-2 ml-5 w-50"
                                [items]="briefing_signature_labels"
                                bindLabel="title"
                                [bindValue]="'id'"
                                placeholder="Select signature setting"
                                name="briefing_signatures_toolbox_talks"
                                [(ngModel)]="project.custom_field.briefing_signatures.toolbox_talks">
                        </ng-select>
                    </div>
                </div>

                <hr>
                <div [ngbTooltip]="projectFeaturePermission.permit === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.permit)">
                    <div class="form-group" *ngIf="this.contractorInfo.features_status && this.contractorInfo.features_status.permit else permitDisable">
                        <div class="custom-control custom-checkbox ml-2" style="z-index: 2;">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.project_section_access.permit"
                                   [checked]="project.project_section_access.permit"
                                   [disabled]="disableToolStatus(projectFeaturePermission.permit)"
                                   name="permits"
                                   id="permits">
                            <label class="custom-control-label" for="permits">Permits</label>
                            <div ngbDropdown [autoClose]="'outside'" class="d-inline-block mr-1" placement="bottom-right" #permitConfig="ngbDropdown">
                                <i class="fas fa-cog cursor-pointer ml-1 dropdown-toggle-no-caret cowSetting" ngbDropdownToggle placement="bottom-left" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="permitConfig"></i>
                                <div ngbDropdownMenu class="dropdown-menu" aria-labelledby="permitConfig">
                                    <button class="dropdown-item cursor-pointer" (click)="openPermitConfigModal()" type="button">Permit Configuration</button>
                                </div>
                            </div>
                        </div>
                        <alternative-phrase-setting #altPhrasePermit [project]="project" [projectPortal]="true"
                                                    [defaultPhrase]="'Permits'" [feature]="'permit'">
                        </alternative-phrase-setting>
                    </div>
                    <ng-template #permitDisable>
                        <div class="form-group" >
                            <div class="custom-control custom-checkbox ml-2" style="z-index: 2;">
                                <input type="checkbox" class="custom-control-input"
                                       disabled
                                       name="permits"
                                       id="permits">
                                <label class="custom-control-label" for="permits">Permit tool can only be configured after the project setup is complete.</label>
                            </div>
                        </div>
                    </ng-template>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.progress_photos === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.progress_photos)">
                    <div class="form-group" [class.disable-tool]="disableToolStatus(projectFeaturePermission.progress_photos)">
                        <div class="custom-control custom-checkbox ml-2">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.project_section_access.progress_photos"
                                   [checked]="project.project_section_access.progress_photos"
                                   [disabled]="disableToolStatus(projectFeaturePermission.progress_photos)"
                                   name="pps"
                                   id="pps">
                            <label class="custom-control-label" for="pps">Progress Photos</label>
                        </div>
                    </div>
                </div>

                <hr>
                <div [ngbTooltip]="projectFeaturePermission.delivery_notes === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.delivery_notes)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.project_section_access.delivery_notes"
                                       [checked]="project.project_section_access.delivery_notes"
                                       [disabled]="disableToolStatus(projectFeaturePermission.delivery_notes)"
                                       name="dns"
                                       id="dns">
                                <label class="custom-control-label" for="dns">{{ project.custom_field.dn_phrase }}</label>
                            </div>
                        </div>
                        <alternative-phrase-setting   #altPhraseDn [project]="project" [projectPortal]="true"
                                                      [defaultPhrase]="'Delivery Notes'" [feature]="'delivery_notes'">
                        </alternative-phrase-setting>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.collection_notes === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.collection_notes)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.project_section_access.collection_notes"
                                       [checked]="project.project_section_access.collection_notes"
                                       [disabled]="disableToolStatus(projectFeaturePermission.collection_notes)"
                                       name="cns"
                                       id="cns">
                                <label class="custom-control-label" for="cns">{{ project.custom_field.cn_phrase }}</label>
                            </div>
                        </div>
                        <alternative-phrase-setting   #altPhraseCn [project]="project" [projectPortal]="true"
                                                      [defaultPhrase]="'Collection Notes'" [feature]="'collection_notes'">
                        </alternative-phrase-setting>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.incident_report === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.incident_report)">
                    <div class="form-group" [class.disable-tool]="disableToolStatus(projectFeaturePermission.incident_report)">
                        <div class="custom-control custom-checkbox ml-2">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.project_section_access.incident_report"
                                   [checked]="project.project_section_access.incident_report"
                                   [disabled]="disableToolStatus(projectFeaturePermission.incident_report)"
                                   name="ir"
                                   id="ir">
                            <label class="custom-control-label" for="ir">Incident Report</label>
                        </div>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.ib_checklist === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.ib_checklist)">
                    <div class="form-group" [class.disable-tool]="disableToolStatus(projectFeaturePermission.ib_checklist)">
                        <div class="custom-checkbox">
                            <inspections-modal-box
                                    [project]="project"
                                    [projectId]="projectId"
                                    [section_access_key]="'project_section_access'"
                                    [settingIcon]="'fa-plus-square'"
                                    [inspections_admin]="projectFeaturePermission.ib_checklist"
                            ></inspections-modal-box>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="mb-1" [ngbTooltip]="projectFeaturePermission.clerk_of_works === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.clerk_of_works)">
                    <div class="custom-control custom-checkbox ml-2" style="z-index: 2;">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="project.project_section_access.clerk_of_works"
                               [checked]="project.project_section_access.clerk_of_works"
                               [disabled]="disableToolStatus(projectFeaturePermission.clerk_of_works)"
                               name="cow"
                               id="cow">
                        <label class="custom-control-label" for="cow">{{ project.cow_setting.cow_phrase }}</label>
                        <div ngbDropdown [autoClose]="'outside'" class="d-inline-block mr-1" placement="bottom-right" #dM2="ngbDropdown">
                            <i class="fas fa-cog cursor-pointer ml-1 dropdown-toggle-no-caret cowSetting" ngbDropdownToggle placement="bottom-left" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="dM2"></i>
                            <div ngbDropdownMenu class="dropdown-menu" aria-labelledby="dM2">
                                <button class="dropdown-item cursor-pointer" (click)="projectSiteConfigModal()" type="button">Configuration</button>
                                <button class="dropdown-item cursor-pointer" (click)="tagOwnersModal()" type="button">Tag Owners</button>
                                <button class="dropdown-item cursor-pointer" (click)="cowSiteDrawingsModal()" type="button">Add Site Drawings</button>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.powra === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.powra)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.project_section_access.powra"
                                       [checked]="project.project_section_access.powra"
                                       [disabled]="disableToolStatus(projectFeaturePermission.powra)"
                                       name="powra"
                                       id="powra">
                                <label class="custom-control-label" for="powra">{{ project.custom_field.powra_phrase }}</label>
                            </div>
                        </div>
                        <alternative-phrase-setting #altPhrasePowra [project]="project" [projectPortal]="true"
                                                    [defaultPhrase]="'POWRA'" [feature]="'powra'">
                        </alternative-phrase-setting>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.rams === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.rams)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.project_section_access.rams"
                                       [checked]="project.project_section_access.rams"
                                       [disabled]="disableToolStatus(projectFeaturePermission.rams)"
                                       name="rams"
                                       id="rams">
                                <label class="custom-control-label" for="rams">{{ project.custom_field.rams_phrase }}</label>
                                <div ngbDropdown [autoClose]="'outside'" class="d-inline-block mr-1" placement="bottom-right" #ramsSettings="ngbDropdown">
                                    <i class="fas fa-cog cursor-pointer ml-1 dropdown-toggle-no-caret cowSetting" ngbDropdownToggle placement="bottom-left" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" id="ramsSettings"></i>
                                    <div ngbDropdownMenu class="dropdown-menu" aria-labelledby="ramsSettings">
                                        <button class="dropdown-item cursor-pointer" (click)="openRamsDocModal()" type="button">{{ project.custom_field.rams_phrase }} Assessment Form</button>
                                        <button *ngIf="this.rams_assessment_ref" class="dropdown-item cursor-pointer alert-danger" (click)="deleteRamsAssessmentForm()" type="button">Delete Form</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <alternative-phrase-setting  #altPhraseRams [project]="project" [projectPortal]="true"
                                                     [defaultPhrase]="'RAMS'" [feature]="'rams'">
                        </alternative-phrase-setting>
                        <div class="custom-control custom-checkbox ml-5">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.custom_field.alternate_userlist_rams"
                                   [checked]="project.custom_field.alternate_userlist_rams"
                                   name="alternate_userlist_rams"
                                   id="alternate_userlist_rams">
                            <label class="custom-control-label" for="alternate_userlist_rams">To take a register, use a list of inducted users rather than on-site users? <i class="fas fa-info-circle" [ngbTooltip]="alternativeUserListTooltip" [openDelay]="200" [closeDelay]="500"></i></label>
                        </div>
                        <ng-select
                                class="mt-2 ml-5 w-50"
                                [items]="briefing_signature_labels"
                                bindLabel="title"
                                [bindValue]="'id'"
                                placeholder="Select signature setting"
                                name="briefing_signatures_rams"
                                [(ngModel)]="project.custom_field.briefing_signatures.rams">
                        </ng-select>
                        <div class="custom-control custom-checkbox mt-2 ml-5">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.custom_field.has_rams_in_induction"
                                   [checked]="project.custom_field.has_rams_in_induction"
                                   name="has_rams_in_induction"
                                   id="has_rams_in_induction">
                            <label class="custom-control-label" for="has_rams_in_induction">Allow {{ project.custom_field.rams_phrase }} to be added into the induction process? <i class="fas fa-info-circle" [ngbTooltip]="'By ticking this on, when a document is added onto the project, a project admin can choose to include in the induction process so that when a user comes to carry out the induction they will have to read and sign'" [openDelay]="200" [closeDelay]="500"></i></label>
                        </div>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.task_briefings === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.task_briefings)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.project_section_access.task_briefings"
                                       [checked]="project.project_section_access.task_briefings"
                                       [disabled]="disableToolStatus(projectFeaturePermission.task_briefings)"
                                       name="tbs"
                                       id="tbs">
                                <label class="custom-control-label" for="tbs">{{ project.custom_field.tb_phrase }}</label>
                            </div>
                        </div>
                        <alternative-phrase-setting   #altPhraseTb [project]="project" [projectPortal]="true"
                                                      [defaultPhrase]="'Task Briefings'" [feature]="'task_briefings'">
                        </alternative-phrase-setting>
                        <div class="custom-control custom-checkbox ml-5">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.custom_field.alternate_userlist_taskbriefings"
                                   [checked]="project.custom_field.alternate_userlist_taskbriefings"
                                   name="alternate_userlist_taskbriefings"
                                   id="alternate_userlist_taskbriefings">
                            <label class="custom-control-label" for="alternate_userlist_taskbriefings">To take a register, use a list of inducted users rather than on-site users? <i class="fas fa-info-circle" [ngbTooltip]="alternativeUserListTooltip"
                                                                                                                                                                                      [openDelay]="200" [closeDelay]="500"></i></label>
                        </div>
                        <ng-select
                                class="mt-2 ml-5 w-50"
                                [items]="briefing_signature_labels"
                                bindLabel="title"
                                [bindValue]="'id'"
                                placeholder="Select signature setting"
                                name="briefing_signatures_task_briefings"
                                [(ngModel)]="project.custom_field.briefing_signatures.task_briefings">
                        </ng-select>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.work_package_plan === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.work_package_plan)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.project_section_access.work_package_plan"
                                       [checked]="project.project_section_access.work_package_plan"
                                       [disabled]="disableToolStatus(projectFeaturePermission.work_package_plan)"
                                       name="wpp"
                                       id="wpp">
                                <label class="custom-control-label" for="wpp">{{ project.custom_field.wpp_phrase }}</label>
                            </div>
                        </div>
                        <alternative-phrase-setting  #altPhraseWpp [project]="project" [projectPortal]="true"
                                                     [defaultPhrase]="'Work Package Plans'" [feature]="'work_package_plan'">
                        </alternative-phrase-setting>
                        <div class="custom-control custom-checkbox ml-5">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.custom_field.alternate_userlist_wpps"
                                   [checked]="project.custom_field.alternate_userlist_wpps"
                                   name="alternate_userlist_wpps"
                                   id="alternate_userlist_wpps">
                            <label class="custom-control-label" for="alternate_userlist_wpps">To take a register, use a list of inducted users rather than on-site users? <i class="fas fa-info-circle" [ngbTooltip]="alternativeUserListTooltip"
                                                                                                                                                                             [openDelay]="200" [closeDelay]="500"></i></label>
                        </div>
                        <ng-select
                                class="mt-2 ml-5 w-50"
                                [items]="briefing_signature_labels"
                                bindLabel="title"
                                [bindValue]="'id'"
                                placeholder="Select signature setting"
                                name="briefing_signatures_wpps"
                                [(ngModel)]="project.custom_field.briefing_signatures.work_package_plans">
                        </ng-select>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.daily_activities === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.daily_activities)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.project_section_access.daily_activities"
                                       [checked]="project.project_section_access.daily_activities"
                                       [disabled]="disableToolStatus(projectFeaturePermission.daily_activities)"
                                       name="da"
                                       id="da">
                                <label class="custom-control-label" for="da">{{ project.custom_field.da_phrase }}</label>
                                <i *ngIf="project.id" class="fas fa-cog cursor-pointer ml-1 cowSetting" (click)="dailyActivitiesImportModal()"></i>
                            </div>
                        </div>
                        <alternative-phrase-setting  #altPhraseDa [project]="project" [projectPortal]="true"
                                                     [defaultPhrase]="'Daily Activities'" [feature]="'daily_activities'">
                        </alternative-phrase-setting>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.delivery_management_status === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.delivery_management_status)">
                    <div class="form-group">
                        <div class="custom-control custom-checkbox ml-2">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.delivery_management_status"
                                   [checked]="project.delivery_management_status"
                                   [disabled]="disableToolStatus(projectFeaturePermission.delivery_management_status)"
                                   name="dms"
                                   id="dms">
                            <label class="custom-control-label" for="dms">Transport Management</label>
                            <a (click)="toggleDeliveryManMenu($event)" class="ml-2">
                                <i class="fa text-primary cursor-pointer" [ngClass]="(isDeliveryManMenuCollapsed) ? 'fa-plus-square' : 'fa-minus-square'"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="form-group collapse" [ngbCollapse]="isDeliveryManMenuCollapsed">
                    <div class="forsCompliant ml-4">
                        <label><strong>FORS Check:</strong></label>
                        <div class="custom-control p-0">
                            <label class="col-form-label form-control-label">Is this a FORS compliant site?</label>
                        </div>
                        <div class="col-md-8 form-inline">
                        <div class="custom-control custom-radio mr-3">
                            <input type="radio" class="custom-control-input" name="ifc" id="ifc-yes"
                                   [required]="project.delivery_management_status" [(ngModel)]="project.is_fors_compliant" [value]="true" />
                            <label class="custom-control-label" for="ifc-yes">Yes</label>
                        </div>
                        <div class="custom-control custom-radio">
                            <input type="radio" class="custom-control-input" name="ifc" id="ifc-no"
                                   [required]="project.delivery_management_status" [(ngModel)]="project.is_fors_compliant" [value]="false" />
                            <label class="custom-control-label" for="ifc-no">No</label>
                        </div>
                    </div>
                    </div>
                    <div class="ml-4 mt-3" *ngIf="project.delivery_management_status">
                        <!-- <label><strong>Delivery Managers:</strong></label> -->
                        <table class="table table-bordered mb-0">
                            <thead>
                            <tr>
                                <th>Delivery Managers <i class="fa fa-info-circle small"
                                    [ngbTooltip]="deliveryManagerTooltip"
                                    [openDelay]="200" [closeDelay]="500"></i> </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr *ngFor="let item of delivery_managers; trackBy : trackByRowIndex; let i = index;">
                                <td [ngModelGroup]="'delivery-admin'+i">
                                    <user-search-box
                                            [userEmail]="(!item._email ? (item.user_ref && item.user_ref.email) : item._email)"
                                            [placeholder]="(!item._email ? (item.user_ref && item.user_ref.name) : item._name)"
                                            [required]="true"
                                            [beforeGetUserInfo]="checkIfAlreadyExist.bind(this, 'delivery_managers', i)"
                                            (validityChanged)="validityChanged($event, 'delivery_managers', i)"
                                            (inputChanged)="onValidDeliveryAdminEmailInput($event, i)">
                                    </user-search-box>
                                    <input type="text" class="d-none" [name]="'d-has-invalid'+i" [required]="true" [ngModel]="item._isValid ? true : undefined"/>
                                </td>
                                <td class="text-center align-middle">
                                    <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeDeliveryManagerRow($event, i)">
                                        delete
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="8"><i class="fa fa-plus-circle text-primary cursor-pointer" (click)="addDeliveryManagerRow()">Add Delivery Manager</i></td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                    <div class="addMoreSlots ml-4 mt-3">
                        <label><strong>Handling Equipment:</strong></label>
                        <div class="custom-control p-0">
                            <label class="col-form-label form-control-label">Click on the button below to add handling equipment</label>
                        </div>
                        <button type="button" class="btn btn-primary pb-1 pt-1 mb-2 " title="Add Equipment Equipment" (click)="openEquipmentModal()" >Add Handling Equipment</button>
                    </div>
                    <div class="addMoreSlots ml-4 mt-3">
                        <label><strong>Default Booking Slot:</strong></label>
                        <div class="col-md-8 form-inline pl-0">
                            <div class="custom-control custom-radio mr-2">
                                <input type="radio" class="custom-control-input" name="default_booking_slot" id="30_minutes"
                                   required [value]="30"  (click)="changeMinTimeSlot(30)" [(ngModel)]="project.custom_field.default_booking_slot"/>
                            <label class="custom-control-label radio-btn-label" for="30_minutes">30 minutes</label>
                            </div>
                            <div class="custom-control custom-radio">
                                <input type="radio" class="custom-control-input" name="default_booking_slot" id="15_minutes"
                                   required [value]="15"  (click)="changeMinTimeSlot(15)" [(ngModel)]="project.custom_field.default_booking_slot"/>
                                <label class="custom-control-label radio-btn-label" for="15_minutes">15 minutes</label>
                            </div>
                        </div>
                    </div>
                    <div class="addMoreSlots ml-4 mt-3">
                        <label><strong>Maximum Booking Time:</strong></label>
                        <div class="custom-control p-0 mb-1">
                            <div class="custom-control custom-checkbox ml-1">
                                <input type="checkbox" class="custom-control-input"
                                       [(ngModel)]="project.custom_field.maximum_booking_status"
                                       [checked]="project.custom_field.maximum_booking_status"
                                       name="mbt"
                                       id="mbt">
                                <label class="custom-control-label" for="mbt">Would you like to set a maximum booking slot time?</label>
                            </div>
                        </div>

                    </div>
                    <div class="form-group ml-4" *ngIf="project.custom_field.maximum_booking_status">
                        <div class="input-group" >
                            <div class="input-group-prepend">
                                <button class="btn btn-primary" type="button" (click)="decreaseDuration()"><i class="fas fa-minus" title="Decrease Duration"></i></button>
                            </div>
                            <input class="form-control col-md-6" [(ngModel)]="project.custom_field.maximum_booking_time" name="maximum_booking_time"
                                   placeholder="Duration" disabled/>
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button" (click)="increaseDuration()"><i class="fas fa-plus" title="Increase Duration"></i></button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group ml-4 mt-3 custom-control custom-checkbox d-inline-block">
                        <input type="checkbox" class="custom-control-input"
                               [(ngModel)]="project.custom_field.booking_approval_process"
                               [checked]="project.custom_field.booking_approval_process"
                               name="approvalprocess"
                               id="approvalprocess">
                        <label class="custom-control-label" for="approvalprocess">Activate approval process</label>
                        <i title="This will require all bookings to be reviewed and approved by the project Delivery Manager/s. This will allow multiple bookings to be made on the same slot for review. If this is switched off, only one booking can be made per slot but can be deleted or amended by the project Delivery Manager/s." class="ml-1 fas fa-info-circle"></i>
                    </div>
                    <div class="addMoreSlots ml-4 mt-3">
                        <div><label><strong>Gate Configuration:</strong></label></div>
                        <label class="font-weight-bold">Add more gates:</label>
                        <i class="fa fa-plus-circle text-primary ml-6 cursor-pointer" (click)="addGateRow()"></i>
                    </div>
                    <div *ngFor="let item of project.project_gates; trackBy : trackByRowIndex; let i = index;">
                        <div class="gateContainer mt-3">
                            <a (click)="$event.stopPropagation(); isGateBookingMenuCollapsed[i] = !isGateBookingMenuCollapsed[i]">
                                <i class="fa text-primary" [ngClass]="{'fa-plus-square': isGateBookingMenuCollapsed[i],'fa-minus-square': !isGateBookingMenuCollapsed[i]}"></i>
                            </a>
                            <span class="material-symbols-outlined float-right text-danger cursor-pointer" (click)="removeGateRow(i)">
                                delete
                            </span>
                            <input type="text" [name]="'gate_name' + i"
                                   [required]="project.delivery_management_status" class="form-control col-md-6 ml-2 d-inline-block" #pGateName="ngModel" [(ngModel)]="project.project_gates[i].gate_name"
                                   placeholder="Enter Gate Name"
                                   ng-value="project.project_gates[i].gate_name">
                            <button *ngIf="!project.project_gates[i].route_map_file_id" (click)="gateRouteMap(project.project_gates[i], i)" type="button" class="btn btn-primary pl-2 pr-2 pb-1 pt-1 mb-2 ml-2" title="Add Route Map">Add Route Map</button>
                            <span class="btn routeMapIcon" *ngIf="project.project_gates[i].route_map_file_id" style="cursor:pointer;" (click)="gateRouteMap(project.project_gates[i], i)">
                                <i title="View Route Map" class="fas fa-map-marked-alt dark"></i>
                            </span>
                            <div class="collapse mt-3 ml-4" #toggleGateContainer [ngbCollapse]="isGateBookingMenuCollapsed[i]">
                                <label>Choose your available slots:</label>
                                <table class="table table-bordered table-dark text-center">
                                    <thead>
                                    <tr>
                                        <td></td>
                                        <td *ngFor="let item of daysInWeek; trackBy : trackByRowIndex; let j = index;" [ngClass]="{'timeSlotUnselected timeSlots font-weight-bold': true, timeSlotSelected: dayIsSelected(i, daysInWeek[j])}">
                                            <div (click)="selectAllSlots(i, daysInWeek[j])" class="disable-text-select">{{ daysInWeek[j] }}</div>
                                        </td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr *ngFor="let item of timeSlots; trackBy : trackByRowIndex; let k = index;">
                                        <td class="font-weight-bold" class="disable-text-select">{{ timeSlots[k] }}</td>
                                        <td *ngFor="let item of daysInWeek; trackBy : trackByRowIndex; let l = index;" [ngClass]="{'timeSlotUnselected timeSlots': true, timeSlotSelected: slotIsSelected(i, daysInWeek[l], timeSlots[k])}">
                                            <div (dragover)="slotClicked(i, daysInWeek[l], timeSlots[k])" (click)="unSelectSlot(i, daysInWeek[l], timeSlots[k])" draggable="true"></div>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.asset_management === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.asset_management)">
                    <div class="form-group">
                        <div class="custom-checkbox">
                            <assets-modal-box
                                    [project]="project"
                                    [section_access_key]="'project_section_access'"
                                    [settingIcon]="'fa-plus-square'"
                                    [asset_management_admin]="projectFeaturePermission.asset_management"
                            ></assets-modal-box>
                        </div>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.fatigue_management_status === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.fatigue_management_status)">
                    <div class="form-group">
                        <div class="custom-control custom-checkbox ml-2 d-inline-block">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.fatigue_management_status"
                                   [checked]="project.fatigue_management_status"
                                   [disabled]="disableToolStatus(projectFeaturePermission.fatigue_management_status)"
                                   name="fatiguems"
                                   id="fatiguems">
                            <label class="custom-control-label" for="fatiguems">Fatigue Management</label>
                            <a (click)="toggleFatigueManMenu($event)" class="ml-2">
                                <i class="fa text-primary cursor-pointer" [ngClass]="(isFatigueManMenuCollapsed) ? 'fa-plus-square' : 'fa-minus-square'"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <hr>
                <div class="form-group collapse ml-2" [ngbCollapse]="isFatigueManMenuCollapsed">
                    <div class="ml-4 mt-3">
                        <div><label><strong>Fatigue Managers:</strong></label></div>
                        <table class="table table-bordered mb-0">
                            <thead>
                            <tr>
                                <th>Fatigue Managers <i class="fa fa-info-circle small"
                                    [ngbTooltip]="fatigueManagerTooltip"
                                    [openDelay]="200" [closeDelay]="500"></i> </th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr *ngFor="let item of fatigue_managers; trackBy : trackByRowIndex; let i = index;">
                                <td [ngModelGroup]="'d-admin'+i">
                                    <user-search-box
                                            [userEmail]="(!item.email ? (item.user_ref && item.user_ref.email) : item.email)"
                                            [placeholder]="(!item.email ? (item.user_ref && item.user_ref.name) : item.name)"
                                            [required]="true"
                                            [beforeGetUserInfo]="checkIfFatigueMgrAlreadyExist.bind(this, 'fatigue_managers', i)"
                                            (validityChanged)="validityChanged($event, 'fatigue_managers', i)"
                                            (inputChanged)="onValidFatigueAdminEmailInput($event, i)">
                                    </user-search-box>
                                    <input type="text" [disabled]="!project.fatigue_management_status" class="d-none" [name]="'d-has-invalid'+i" [required]="true" [ngModel]="item._isValid ? true : undefined"/>
                                </td>
                                <td class="text-center align-middle">
                                    <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeFatigueManagerRow($event, i)">
                                        delete
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="8"><i class="fa fa-plus-circle text-primary cursor-pointer" (click)="addFatigueManagerRow()">Add Fatigue Manager</i></td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                    <div class="ml-4 mt-3"><label><strong>Configuration:</strong></label></div>
                    <div class="ml-4 mt-1">
                        <p>The working hours agreement specifies the following:</p>
                        <div class="form-check fatigue-head">
                            <input type="checkbox" name="site_hours_daily_status" [(ngModel)]="site_hours_daily_status" class="form-check-input fatigue-head-checkbox ">
                            <label class="form-check-label fatigue-head-label"> No more than <input placeholder="0" type="number" name="site_hours_daily" [(ngModel)]="project.site_hours_daily" class="fatigue-head-input"> hours on site per day.</label>
                        </div>

                        <div class="form-check fatigue-head">
                            <input type="checkbox" name="total_hours_daily_status" [(ngModel)]="total_hours_daily_status" class="form-check-input fatigue-head-checkbox">
                            <label class="form-check-label fatigue-head-label"> No more than <input placeholder="0" type="number" name="total_hours_daily" [(ngModel)]="project.total_hours_daily" class="fatigue-head-input"> hours on site per day including travel.</label>
                        </div>

                        <div class="form-check fatigue-head">
                            <input type="checkbox" name="site_hours_weekly_status" class="form-check-input fatigue-head-checkbox" [(ngModel)]="site_hours_weekly_status">
                            <label class="form-check-label fatigue-head-label"> No more than <input placeholder="0" type="number" name="site_hours_weekly" [(ngModel)]="project.site_hours_weekly" class="fatigue-head-input">  hours to be worked in any 7 day period.</label>
                        </div>

                        <div class="form-check fatigue-head">
                            <input type="checkbox" name="total_hours_shifts_status" class="form-check-input fatigue-head-checkbox" [(ngModel)]="total_hours_shifts_status">
                            <label class="form-check-label fatigue-head-label"> No less than&nbsp;&nbsp;&nbsp;<input placeholder="0" type="number" name="total_hours_shifts" [(ngModel)]="project.total_hours_shifts" class="fatigue-head-input"> hours rest between shifts.</label>
                        </div>

                        <div class="form-check fatigue-head">
                            <input type="checkbox" name="total_duty_periods_biweekly_status" class="form-check-input fatigue-head-checkbox" [(ngModel)]="total_duty_periods_biweekly_status">
                            <label class="form-check-label fatigue-head-label"> No more than <input placeholder="0" type="number" name="total_duty_periods_biweekly" [(ngModel)]="project.total_duty_periods_biweekly" class="fatigue-head-input"> periods of duty to be worked in any 14 day period.</label>
                        </div>
                    </div>
                </div>
                <div [ngbTooltip]="projectFeaturePermission.resource_planner === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.resource_planner)">
                    <div class="form-group" [class.disable-tool]="disableToolStatus(projectFeaturePermission.resource_planner)">
                        <div class="custom-control custom-checkbox ml-2 d-inline-block">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.project_section_access.resource_planner"
                                   [checked]="project.project_section_access.resource_planner"
                                   [disabled]="disableToolStatus(projectFeaturePermission.resource_planner)"
                                   name="resource_planner"
                                   id="resource_planner">
                            <label class="custom-control-label" for="resource_planner">Resource Planner</label>
                        </div>
                    </div>
                </div>
                <hr>
                <div [ngbTooltip]="projectFeaturePermission.documents_tool === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body"
                     [disableTooltip]="disableToolTipStatus(projectFeaturePermission.documents_tool)">
                    <div class="form-group" [class.disable-tool]="disableToolStatus(projectFeaturePermission.documents_tool)">
                        <div class="custom-control custom-checkbox ml-2">
                            <input type="checkbox" class="custom-control-input"
                                   [(ngModel)]="project.project_section_access.documents_tool"
                                   [checked]="project.project_section_access.documents_tool"
                                   [disabled]="disableToolStatus(projectFeaturePermission.documents_tool)"
                                   name="documents_tool"
                                   id="documents_tool">
                            <label class="custom-control-label" for="documents_tool">Documents</label>
                        </div>
                    </div>
                </div>

                <hr>
                <div [ngbTooltip]="projectFeaturePermission.quality_checklist === 'lock-on' ? featureSettingToolTipForLockOn : featureSettingToolTipForLockOff" [placement]="tooltipPlacement" container="body" [disableTooltip]="disableToolTipStatus(projectFeaturePermission.quality_checklist)">
                    <div class="form-group">
                        <div class="mb-1">
                            <div class="custom-control custom-checkbox ml-2" [class.disable-tool]="disableToolStatus(projectFeaturePermission.quality_checklist)" style="z-index: 2;">
                                <itp-modal-box [project]="project" [itp_admin]="projectFeaturePermission.quality_checklist"></itp-modal-box>
                            </div>
                        </div>
                        <alternative-phrase-setting   #altPhraseQcl [project]="project" [projectPortal]="true"
                                                      [defaultPhrase]="'ITPs'" [feature]="'quality_checklist'">
                        </alternative-phrase-setting>
                    </div>
                </div>
            </form>

            <block-loader [show]="(loadingInProgress)" alwaysInCenter="true" showBlockBackdrop="true"></block-loader>
            <div class="stepwizard form-group">
                <hr/>
                <button type="button" *ngIf="activeStage" class="btn btn-secondary" (click)="movePrev()">Back</button>
                <button type="submit" *ngIf="!isNotThisStage('Information')" [disabled]="!infoForm.valid"
                        class="btn btn-primary float-right" (click)="moveNext()">Next</button>
                <!--<button type="submit" *ngIf="!isNotThisStage('Select Fields')" [disabled]="!selectFieldsForm.valid"
                        class="btn btn-primary float-right" (click)="moveNext()">Next</button>-->

                <button type="submit" *ngIf="!isNotThisStage('Select Template')" [disabled]="!selectTemplateForm.valid"
                        class="btn btn-primary float-right" (click)="moveNext()">Next</button>
                <button type="submit" *ngIf="!isNotThisStage('Induction Settings')" [disabled]="!mediaForm.valid || (project?.custom_field?.has_supply_chain_companies && !isSelectedSCValid)"
                        class="btn btn-primary float-right" (click)="moveNext()">Next</button>
                <button type="submit" *ngIf="!isNotThisStage('Access')" [disabled]="!accessForm.valid"
                        class="btn btn-primary float-right" (click)="moveNext()">Next</button>
                <button type="submit" *ngIf="!isNotThisStage('Site Policies')" [disabled]="!policiesForm.valid"
                        class="btn btn-primary float-right" (click)="moveNext()">Next</button>
                <!--<button type="submit" *ngIf="!isNotThisStage('Declarations')" [disabled]="!declarationForm.valid"
                        class="btn btn-primary float-right" (click)="moveNext()">Next</button>-->

                <!--<button type="submit" *ngIf="activeStage !== formStages.length - 1" [disabled]="!newProjectForm.valid"
                        class="btn btn-primary float-right" (click)="moveNext()">Next</button>-->
                <button type="submit" *ngIf="!isNotThisStage('Declarations')" [disabled]="!declarationForm.valid"
                        class="btn btn-primary float-right" (click)="moveNext()">Next</button>
                <button type="submit" *ngIf="activeStage === formStages.length - 1" [disabled]="!addOnsForm.valid"
                        class="btn btn-primary float-right" (click)="saveProject(addOnsForm)">Save</button>
            </div>
        </div>
    </div>
    <i-modal #routeMapHtml [title]="selectedGateDetail.gate_name + ' Route Map'" size="lg" [showCancel]="true" [cancelBtnText]="'Close'" (onCancel)="closeRouteMapModal()">
            <div class="form-group row" *ngIf="showRouteMapModal">
                <div class="col-sm-12 d-flex justify-content-center">
                    <file-uploader-v2
                        [disabled]="false"
                        [category]="'gate-route-map'"
                        [showDeleteBtn]="true"
                        [hasImgAndDoc]="true"
                        [init]="selectedGateDetail" [dragnDropTxt]="'Drag and drop image or pdf here'"
                        (uploadDone)="routeMapUploadDone($event)"
                        (deleteFileDone)="deleteRouteMapRecord($event, routeMapUploader)"
                        [allowedMimeType]="['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']"
                        #routeMapUploader>
                    </file-uploader-v2>
                </div>
                <div class="col-sm-12 mt-2 text-center" *ngIf="selectedGateDetail.route_map_file_url">
                    <div *ngIf="!isPdfDocument" style="width: 600px;" class="d-inline-block">
                        <img [src]="selectedGateDetail.route_map_file_url" (error)="onLogoError(img, selectedGateDetail.route_map_file_url, false)" (load)="onLogoLoad(img)" style="width: 100%; height: auto;" #img />
                    </div>

                    <iframe *ngIf="selectedGateDetail.route_map_file_url && isPdfDocument" class="border-0" [src]="previewURL" width="750px" height="500px">
                    </iframe>
                </div>
            </div>
    </i-modal>

    <i-modal #handlinEquipmentHtml title="Add Handling Equipment" size="md" [showCancel]="false" (onClickRightPB)="addHandlingEquipment($event)"
        rightPrimaryBtnTxt="Save">
            <form novalidate #handlinEquipmentForm="ngForm">
                <div class="form-group">
                    <table class="table table-bordered">
                        <thead>
                            <th class="text-center">Equipment</th>
                            <th class="text-center">Action</th>
                        </thead>
                        <tbody>
                            <ng-container *ngFor="let item of project.custom_field.handling_equipment; trackBy : trackByRowIndex; let i = index;" >
                                <tr>
                                    <td>
                                        <input *ngIf="i == 0" type="text" class="form-control" name="categoryName{{i}}" [(ngModel)]="project.custom_field.handling_equipment[i]"
                                        placeholder="Enter equipment then press Add" autocomplete="off" required/>
                                        <input *ngIf="i != 0" type="text" class="form-control" name="categoryName{{i}}" [(ngModel)]="project.custom_field.handling_equipment[i]"
                                        placeholder="Enter equipment" autocomplete="off" />

                                    </td>
                                    <td *ngIf="i == 0" class="text-center">
                                        <button class="btn btn-sm btn-outline-primary" (click)="addEquipment(i)" [disabled]="!handlinEquipmentForm.valid">
                                            <i class="fa fa-plus"></i> Add
                                        </button>
                                    </td>
                                    <td *ngIf="i > 0" class="text-center">
                                        <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeEquipment(i)" *ngIf="!(i == 0)">
                                            delete
                                        </span>
                                    </td>
                                </tr>
                            </ng-container>
                        </tbody>
                    </table>
                </div>
            </form>
    </i-modal>


</div>

<company-site-config-modal
    #projectSiteConfigPopup
    [project]="project">
</company-site-config-modal>

<daily-activities-import-modal
    #dailyActivitiesImportModalHtml
    [project]="project">
</daily-activities-import-modal>

<cow-owener-tagging-modal
    #cowOwenerTaggingPopup
    [project]="project">
</cow-owener-tagging-modal>

<cow-site-drawings-modal
    #cowSiteDrawingsPopup
    [project]="project">
</cow-site-drawings-modal>

<tag-owner-modal
    #closeCallTagOwnerModal
    [project]="project"
    [users_employer]="users_employer"
    [field_to_update]="'closecall_setting'"
    [feature_name]="project.custom_field.cc_phrase"
>
</tag-owner-modal>

<tag-owner-modal
    #goodCallTagOwnerModal
    [project]="project"
    [users_employer]="users_employer"
    [field_to_update]="'goodcall_setting'"
    [feature_name]="'Good Call'"
>
</tag-owner-modal>

<tag-owner-modal
    #observationsTagOwnerModal
    [project]="project"
    [users_employer]="users_employer"
    [field_to_update]="'custom_field'"
    [key_in_field]="'obsr_tagged_companies'"
    [feature_name]="'Observations'"
>
</tag-owner-modal>

<manage-categories-modal
    #observationsCategoriesModal
    [inputRecords]="allObervationCategories"
    [feature_name]="project.custom_field.obrs_phrase"
    (updatedRecords)="onSavingObservationCategories($event)"
>
</manage-categories-modal>

<i-modal #uploadRamsDocModal title="Configure RAMS Assessment Form" size="lg" [windowClass]="'l-modal'" [cancelBtnText]="'Close'" [rightPrimaryBtnDisabled]="!uploadRamsDocModalForm.valid" (onClickRightPB)="validateRamsAssessmentForm(uploadRamsDocModalForm, $event)"
    rightPrimaryBtnTxt="Save">
        <form novalidate #uploadRamsDocModalForm="ngForm">
            <div class="form-group">
                <label><strong>Upload File (PDF)<small class="required-asterisk ">*</small></strong></label>
                <input type="hidden" name="rams_assessment_ref" id="rams_assessment_ref"
                       [(ngModel)]="rams_assessment_ref" required="true"/>
                <file-uploader-v2
                    [init]="rams_assessment_file"
                    [dragnDropTxt]="'Drag and drop pdf here'"
                    (uploadDone)="uploadRamsAssessmentDone($event)"
                    [allowedMimeType]="['application/pdf', 'application/x-pdf']"
                    (deleteFileDone)="deleteRamsAssessment($event)"
                    [showDeleteBtn]="true"
                    [showDragnDrop]="true"
                    [disabled]="false"
                    [showHyperlink]="true"
                    [showFileFullName]="true"
                    [category]="'rams-assessment-form'"
                ></file-uploader-v2>
            </div>

            <ng-container *ngIf="rams_assessment_ref && ramsAssessmentFormFields.length">
                <div class="form-group col-md-5 p-0">
                    <label>Font size of fillable fields<small class="required-asterisk">*</small></label>
                    <ng-select name="font_size" [(ngModel)]="rams_assessment_font_size" required>
                        <ng-option *ngFor="let num of fontSizeOptions" [value]="num">{{ num }}</ng-option>
                    </ng-select>
                </div>

                <div class="form-group">
                    <label class="font-weight-bold">The system found the following fillable fields from the uploaded PDF. Please cross-check each field's label and the type and put the correct one if needed:</label>
                    <div class="d-block">
                        <div class="col-12 p-0 mb-2 heading-list">
                            <div class="row mx-0">
                                <div class="col-4 p-1 text-center">Name</div>

                                <div class="col-5 p-1 text-center">Label</div>

                                <div class="col-3 p-1 text-center">Type</div>
                            </div>
                        </div>

                        <div class="col-12 p-0 mb-2 ramsfields-list" dragula="ramsfields" [(dragulaModel)]="ramsAssessmentFormFields">
                            <ng-template ngFor let-item [ngForOf]="(ramsAssessmentFormFields || [])" let-i="index">
                                <div class="drag-box py-2">
                                    <div [class]="'row mx-0 rowId_'+i" [id]="'rowId_'+i">
                                        <div class="col-4 p-1">
                                            <div class="col-1 text-center align-self-center p-0 d-inline-block">
                                                <i class="fa fa-bars" aria-hidden="true"></i>
                                            </div>
                                            <div class="col-11 d-inline-block">
                                                <div class="row p-0">
                                                    <input type="text" class="form-control col-d11"
                                                           [name]="'fieldName_'+item.field_id+'_'+i"
                                                           [ngModel]="item.field_name"
                                                           placeholder="Enter Name" autocomplete="off"
                                                           [required]="isRequired(i)" readonly/>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-5 p-1">
                                            <input type="text" class="form-control col-d11"
                                                   [name]="'fieldLabel_'+item.field_id+'_'+i"
                                                   [(ngModel)]="item.field_label"
                                                   placeholder="Enter Label" autocomplete="off"
                                                   [required]="isRequired(i)"/>
                                            <div class="custom-control custom-checkbox pt-1 medium-font">
                                                <input type="checkbox" class="custom-control-input"
                                                       [name]="'is_mandatory_'+item.field_id+'_'+i"
                                                       [id]="'is_mandatory_'+item.field_id+'_'+i"
                                                       [(ngModel)]="item.is_mandatory"/>
                                                <label class="custom-control-label" [for]="'is_mandatory_'+item.field_id+'_'+i"
                                                       style="padding-top: 2px;">Is it mandatory field?</label>
                                            </div>
                                        </div>

                                        <div class="col-3 p-1">
                                            <ng-select [name]="item.field_id+'_'+i" [(ngModel)]="item.field_type" class="form-control show-full-txt-option" [required]="isRequired(i)">
                                                <ng-container *ngFor="let t of ramsAssessmentFormFieldTypes">
                                                    <ng-option [value]="t.key" [disabled]="(signatureTypes.includes(t.key) && !signatureTypes.includes(item.field_type)) || (!signatureTypes.includes(t.key) && signatureTypes.includes(item.field_type))">
                                                        <span>{{ t.label }}</span>
                                                    </ng-option>
                                                </ng-container>
                                            </ng-select>

                                            <ng-select *ngIf="item.field_type == 'data_field'" [(ngModel)]="item.data_field"
                                                    [name]="item.data_field+'_'+i"
                                                    [items]="predefinedDataFields"
                                                    bindLabel="label" bindValue="key"
                                                    class="form-control mt-2 show-full-txt-option" required
                                                    ng-value="item.data_field">
                                            </ng-select>

                                            <label *ngIf="['selectbox', 'multi_select'].includes(item.field_type)" class="medium-font cursor-pointer pt-1 m-0"
                                                   (click)="manageCustomSelectOption(i)">
                                                <i class="fas fa-edit text-primary mr-1"></i>Edit dropdown options
                                            </label>
                                        </div>
                                        <!--<div class="col-1 p-1 text-center">
                                            <i class="far fa-trash-alt text-danger cursor-pointer"
                                               (click)="removeAssessmentFormField(i)"></i>
                                        </div>-->
                                    </div>
                                </div>
                            </ng-template>
                        </div>
                        <!--<div class="input-group">
                            <span class="text-primary cursor-pointer" (click)="addAssessmentFormField()"><i class="fa fa-plus-circle"></i>Add Field</span>
                        </div>-->
                    </div>
                </div>
            </ng-container>
        </form>
</i-modal>

<i-modal #customSelectOptionsModal title="Dropdown Options" size="md" [showCancel]="false" [rightPrimaryBtnDisabled]="!optionForm.valid" (onClickRightPB)="saveOptions($event)"
rightPrimaryBtnTxt="Save">
        <form novalidate #optionForm="ngForm">
            <div class="px-4">
                <span>Add, remove options</span>
                <div class="form-group row mx-0">
                    <table class="table table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">Options</th>
                            <th class="text-center">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <ng-container *ngFor="let option of ramsAssessmentSelectField.options trackBy : trackByRowIndex; let i = index;">
                            <tr>
                                <td>
                                    <div>
                                        <input *ngIf="i == 0" type="text" class="form-control" [name]="'optionLabel' + i"
                                               #optionLabel="ngModel" [(ngModel)]="option.label"
                                               placeholder="Enter option label then press add" autocomplete="off" />
                                        <input *ngIf="i != 0" type="text" class="form-control" [name]="'optionLabel' + i"
                                               #optionLabel="ngModel" [(ngModel)]="option.label"
                                               placeholder="Enter option" autocomplete="off" required=""/>
                                    </div>
                                </td>
                                <td *ngIf="i == 0" class="text-center">
                                    <button class="btn btn-sm btn-outline-primary" (click)="addCustomSelectOption(i)" [disabled]="!option?.label">
                                        <i class="fa fa-plus"></i> Add
                                    </button>
                                </td>
                                <td *ngIf="i > 0" class="text-center">
                                    <span class="material-symbols-outlined text-danger cursor-pointer" (click)="removeCustomSelectOption(i)" *ngIf="!(i == 0)">
                                        delete
                                    </span>
                                </td>
                            </tr>
                        </ng-container>
                        </tbody>
                    </table>
                </div>
            </div>
        </form>
</i-modal>
<generic-confirmation-modal #confirmationModalRef></generic-confirmation-modal>

<ng-template #districtAndAreaCodesModal let-c="close" let-d="dismiss">
    <div class="modal-header">
        <h4 class="modal-title">
            District Area Code Breakdown
        </h4>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
            <span type="button" class="close-icon small">
                <span aria-hidden="true" style="padding: 1px 7px;">×</span>
            </span>
        </button>
    </div>
    <div class="modal-body px-5">
        <table class="table table-bordered">
            <thead>
            <tr>
                <th class="tr-bg-dark-color vertical-align-middle"> District </th>
                <th class="tr-bg-dark-color vertical-align-middle"> Area Code </th>
            </tr>
            </thead>
            <tbody>
            <tr *ngFor="let item of meta_districts; trackBy : trackByRowIndex; let i = index;">
                <td>{{item.district_name}}</td>
                <td>{{item.area_codes.join(', ')}}</td>
            </tr>
            </tbody>
        </table>

    </div>
    <div class="modal-footer py-1">
        <button type="button" class="btn btn-brandeis-blue" (click)="d('Cross click')">
            Done
        </button>
    </div>
</ng-template>

<!-- Permit Configuration Modal -->
<ng-template #permitConfigModal let-d="dismiss" let-c="close">
    <div class="modal-header">
        <span class="modal-title fw-500"> Permits </span>
        <button type="button" class="close" aria-label="Close" (click)="d('Cross click')">
                <span type="button" class="close-icon small">
                    <span aria-hidden="true" style="padding: 1px 7px;">×</span>
                </span>
        </button>
    </div>
    <div class="modal-body py-0">
        <form novalidate #permitConfigForm="ngForm" style="height: 100%">
            <div class="row gx-1 heightInherit" *ngIf="(project.id && projectPermitTemplates.length) else permitConfigAlert">
                <div [ngClass]="{'py-5 px-5': true, 'col-6 br-light-silver modal-scroll': showPermitConfig, 'col-12': !showPermitConfig}" style="max-height: 82vh;">
                    <div class="form-group">
                        <label class="large-font" style="font-weight: 600;">Permit Types</label>
                    </div>
                    <ng-template ngFor let-permit [ngForOf]="(projectPermitTemplates || [])" let-i="index">
                        <div class='col-12 p-0'>
                            <div class="col-11 d-inline-block p-0" style="font-size: 14px" (click)="togglePermitFieldDisability($event, i)">
                                <label class="mb-0">
                                    <span>{{permit.ref_number}} - {{permit.permit_type}}</span>
                                </label>
                            </div>
                            <div class="col-1 p-0 d-inline-block cursor-pointer mb-1">
                                <label class="checkbox-switch-v2 mb-0 float-right">
                                    <input type="checkbox"
                                           [name]="'permit_active_'+i"
                                           [id]="'permit_active_'+i"
                                           [(ngModel)]="permit.is_active"
                                           (click)="onDisablingPermit(!permit.is_active, permit.id)">
                                    <span class="slider-v2 round"></span>
                                </label>
                            </div>
                        </div>
                        <div class='col-12 p-0 medium-font' *ngIf="permit.is_active">
                            <label class="mb-0 configureSignOffLink">
                                <span *ngIf="!activePermits.includes(permit.permit_ref) && permit.is_active">Configure permit specific managers <a class="font-weight-bold" href="javascript:void(0)" (click)="togglePermit(true, permit, i)">here</a></span>
                                <span *ngIf="activePermits.includes(permit.permit_ref) && permit.is_active">Permit Specific Managers Configured. Click <a class="font-weight-bold text-info" href="javascript:void(0)" (click)="togglePermit(true, permit, i)">here</a> to edit.</span>
                            </label>
                        </div>
                        <hr class="my-2">
                    </ng-template>


                    <div class="form-group mb-0 pt-2">
                        <label style="font-weight: 600;" class="large-font">
                            Master Permit Manager(s) <span class="material-symbols-outlined medium-font mx-1 cursor-pointer vertical-align-middle" style="padding-bottom: 2px;" [ngbTooltip]="'Select a Master permit manager, rather than having to select individual permit managers.'">info</span>
                        </label>
                        <inducted-admin-selector
                                [required]="true"
                                [multiple]="true"
                                name="master_permit_manager"
                                placeholder="Select master permit managers"
                                class="w-100"
                                [projectId]="projectId"
                                [selectId]="masterPermitManagers"
                                (selectionChanged)="onSelectPermitManager($event)"
                        ></inducted-admin-selector>
                    </div>
                </div>
                <div class="py-5 px-5 col-6 modal-scroll" *ngIf="showPermitConfig"  style="max-height: 82vh;">
                    <label class="mb-4 large-font" style="font-weight: 600;">{{selectedPermitTemplate.permit_type}}</label>

                    <div class="col-12 p-0">
                        <label class="medium-font" style="font-weight: 600;">Permit Specific Managers</label>
                        <div class='row mx-0 mb-2 mt-2'>
                            <div class="medium-font col-6 p-0 pl-2" style="font-weight: 400;">
                                Sign-off steps
                            </div>

                            <div class="col-6 p-0 medium-font" style="font-weight: 400;">
                                Select Signatories
                            </div>
                        </div>
                        <ng-template ngFor let-sign [ngForOf]="(projectPermits[selectPermitIndex].sign_off || [])" let-i="index">
                            <div class='row mx-0 mb-2' [ngClass]="{'row mx-0 mb-2': true, 'd-none': sign.is_requestor || sign.is_closeout_requestor ||  sign.is_closeout}">
                                <div class="col-6 p-0 pr-1">
                                    <span class="form-control" style="color: var(--spanish-gray); height: auto;">{{sign.field_label || sign.sign_number}}</span>
                                </div>

                                <div class="col-6 p-0">
                                    <inducted-admin-selector
                                            [multiple]="true"
                                            [name]="'signatories_'+i"
                                            placeholder="Select Signatories"
                                            class="w-100"
                                            [projectId]="projectId"
                                            [selectId]="sign.signatories"
                                            (selectionChanged)="onSelectSignatories($event, i)"
                                    ></inducted-admin-selector>
                                </div>
                            </div>
                        </ng-template>
                    </div>

                    <div class="col-12 p-0 mt-3" *ngIf="permitHasCloseout">
                        <div class='row mx-0 mb-2 mt-2'>
                            <div class="medium-font col-6 p-0 pl-2" style="font-weight: 400;">
                                Closeout steps
                            </div>
                        </div>
                        <ng-template ngFor let-sign [ngForOf]="(projectPermits[selectPermitIndex].sign_off || [])" let-i="index">
                            <div class='row mx-0 mb-2' [ngClass]="{'row mx-0 mb-2': true, 'd-none': !sign.is_closeout }">
                                <div class="col-6 p-0 pr-1">
                                    <span class="form-control" style="color: var(--spanish-gray); height: auto;">{{sign.field_label || sign.sign_number}}</span>
                                </div>

                                <div class="col-6 p-0">
                                    <inducted-admin-selector
                                            [multiple]="true"
                                            [name]="'signatories_cl_'+i"
                                            placeholder="Select Signatories"
                                            class="w-100"
                                            [projectId]="projectId"
                                            [selectId]="sign.signatories"
                                            (selectionChanged)="onSelectSignatories($event, i)"
                                    ></inducted-admin-selector>
                                </div>
                            </div>
                        </ng-template>
                    </div>
                </div>
            </div>

            <ng-template #permitConfigAlert >
                <div class="row gx-1">
                    <div class='py-4 px-5 col-12 text-center'>
                        {{(!project.id) ? 'This can not be configured until the first project induction has been approved' : 'No permits found to configure'}}
                    </div>
                </div>
            </ng-template>
        </form>
    </div>
    <div class="modal-footer py-1" *ngIf="project.id && projectPermitTemplates.length">
        <button type="button" class="btn" style="font-weight: 500; color: #0066FF;" (click)="d()">
            Cancel
        </button>
        <button type="button" class="btn btn-brandeis-blue" (click)="saveProjectPermitConfig(c)" [disabled]="!permitConfigForm.valid">
            Save
        </button>
    </div>
</ng-template>
<block-loader [show]="(processingRequest)" alwaysInCenter="true" showBlockBackdrop="true"></block-loader>
